
# @generated expo-cli sync-2b81b286409207a5da26e14c78851eb30d8ccbdb
# The following patterns were generated by expo-cli

expo-env.d.ts
# @end expo-cli

# keep types in repo to track them
# **/types/**/*.d.ts
# but map can be ignored and just published on npm
**/types/**/*.d.ts.map
android

yarn.lock
wullup-0561f7281447.json
tamagui-output.css

.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

.turbo

.ultra.cache.json
tmp
*.tsbuildinfo
*.tmp.js
yarn-error.log
dist
tsconfig.tsbuildinfo
node_modules
**/_
**/tests/spec/out
.DS_Store

.next

.vercel

apps/site/out
apps/kitchen-sink/ios

.tamagui

.idea

.env
# local env files
.env.local
.env.development.local
.env.test.localp
.env.production.local

apps/kitchen-sink/ios
apps/kitchen-sink/android

# ignore until videos finalized
apps/site/public/talk/

apps/studio/types/**

.expo
