import {QueryClient, QueryKey} from '@tanstack/react-query';

type InvalidateQueriesParams = {
    queryClient: QueryClient;
    queryKeys: QueryKey[];
};

export async function invalidateQueries({queryClient, queryKeys}: InvalidateQueriesParams): Promise<void> {

    // Invalidate all keys in parallel.
    await Promise.all(
        queryKeys.map((keyArray) =>
            queryClient.invalidateQueries({queryKey: keyArray})
        )
    );
}
