import {api} from './apiConfig';
import * as FileSystem from 'expo-file-system';
import {FileSystemUploadType} from 'expo-file-system';


export interface CompleteUserProfile {
    firstName: string | null,
    lastName: string | null,
    username: string,
    musicProvider: string,
}

export interface UpdateUserProfile {
    firstName: string | null,
    lastName: string | null,
    username: string,
    musicProvider: string,
    bio: string | null,
}


export const completeUserProfile = async (user_data: CompleteUserProfile): Promise<any> => {
    //TODO: transform usages to use updateUserProfile instead of completeUserProfile
    const result = await api.put('/v1/users/me', user_data);
    return result.data;
};


export const updateUserProfile = async (user_data: UpdateUserProfile): Promise<any> => {
    const result = await api.put('/v1/users/me', user_data);
    return result.data;
};


export const uploadImage = async ({uri}: { uri: string }) => {
    let userJwtToken: string | undefined = undefined;
    if (api.defaults.headers.common && typeof api.defaults.headers.common['Authorization'] === 'string') {
        userJwtToken = (api.defaults.headers.common['Authorization'] as string).replace('Bearer ', '');
    }
    return FileSystem.uploadAsync(`${api.defaults.baseURL}/v1/users/profile-picture`, uri, {
        httpMethod: 'POST',
        uploadType: FileSystemUploadType.MULTIPART,
        fieldName: 'file',
        headers: {
            Authorization: `Bearer ${userJwtToken}`
        }
    }).then((res) => JSON.parse(res.body));
}


export const getUserData = async (): Promise<any> => {
    const result = await api.get('/v1/users/me');
    return result.data;
};

export const deleteUserAccount = async (): Promise<any> => {
    const result = await api.delete('/v1/users/me');
    return result.data;
}


export const getUserFeed = async (): Promise<any> => {
    const result = await api.get('/v1/users/feed');
    return result.data;
};


export const updatePNToken = async (token: string): Promise<any> => {
    const result = await api.put('/v1/users/pn/token', {"expoPushToken": token});
    return result.data;
}
