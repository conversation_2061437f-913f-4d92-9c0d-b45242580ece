import {api} from "@/api/apiConfig";


export const getUserSuggestedConnections = async (): Promise<any> => {
    const result = await api.get('/v1/users/connections/suggested');
    return result.data;
};

export const getUserConnections = async (): Promise<any> => {
    const result = await api.get('/v1/users/connections');
    return result.data;
};

export const getPendingConnectionRequests = async (): Promise<any> => {
    const result = await api.get('/v1/users/connections/pending');
    return result.data;
};


export const sendConnectionRequest = async (toUserID: string): Promise<any> => {
    const result = await api.post(`/v1/users/connections/${toUserID}`);
    return result.data;
}

export const updateConnectionRequest = async (connectionRequestId: string, action: 'accepted' | 'rejected'): Promise<any> => {
    const result = await api.put(`/v1/users/connections/${connectionRequestId}?action=${action}`);
    return result.data;
}


export const removeConnection = async (connectionId: string): Promise<any> => {
    console.log(connectionId);
    const result = await api.delete(`/v1/users/connections/${connectionId}`);
    return result.data;
}