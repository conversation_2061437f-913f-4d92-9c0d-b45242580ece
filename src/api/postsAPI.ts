import {api} from './apiConfig';
import {Post} from '@/types/PostTypes'

export const fetchPosts = async (): Promise<Post[]> => {
    const result = await api.get('/v1/posts');
    return result.data;
};

export const createPost = async (songData: any, audioUri: string | null) => {
    const formData = new FormData();

    formData.append('new_post', JSON.stringify(songData));

    if (audioUri) {
        // Create a file object from the URI
        formData.append('audio_comment', {
            uri: audioUri,
            type: 'audio/x-m4a',  // or 'audio/m4a'
            name: 'recording.m4a'
        } as any);
    }

    const response = await api.post('/v1/posts', formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    });

    return response.data;
};

export const updatePostRating = async (postId: string, reaction_type: 'Like' | 'Dislike' | null) => {
    const response = await api.put(`/v1/posts/rating/${postId}?reaction_type=${reaction_type}`);
    return response.data;
};

export const deletePost = async (postId: number): Promise<boolean> => {
    const response = await api.delete(`/v1/posts/${postId}`);
    return response.data;
};

export const getUserLeaderboard = async () => {
    const response = await api.get('/v1/posts/leaderboard');
    return response.data;
}

export const commentOnPost = async (postId: string, comment: string, replyToCommentId?: string) => {
    const payload: any = {comment};
    if (replyToCommentId) {
        payload.reply_to_comment_id = replyToCommentId;
    }
    const response = await api.post(`/v1/posts/${postId}/comments`, payload);
    return response.data;
}

export const reactToComment = async (postId: string, commentId: string, reactionType: 'Like' | 'None') => {
    const response = await api.post(`/v1/posts/${postId}/comments/${commentId}/reaction`, null, {
        params: {reaction_type: reactionType}
    });
    return response.data;
}

export const getUserPostHistory = async () => {
    const response = await api.get('/v1/posts/history');
    return response.data;
}