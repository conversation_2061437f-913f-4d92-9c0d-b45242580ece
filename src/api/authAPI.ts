import {api} from './apiConfig';
import {AppleAuthRequets, AppleAuthResponse, GoogleAuthRequest, GoogleAuthResponse, RefreshTokenResponse} from '@/types/AuthTypes';

export const authWithApple = async (credentials: AppleAuthRequets): Promise<AppleAuthResponse> => {
    const response = await api.post('/v1/auth/apple', credentials);
    return response.data;
};

export const authWithGoogle = async (credentials: GoogleAuthRequest): Promise<GoogleAuthResponse> => {
    const response = await api.post('/v1/auth/google', credentials);
    return response.data;
}


export const refreshAuthToken = async (userRefreshToken: string): Promise<RefreshTokenResponse> => {
    const response = await api.post('/v1/auth/refresh', {
        userRefreshToken
    });
    return response.data;
}