import {api} from './apiConfig'
import {MusicSearchResponse, Song} from '../types/MusicTypes'

// Search query parameters interface
export interface SearchQueryParams {
    query: string;
    limit?: number;
    offset?: number;
}

// User search response interface
export interface UserSearchResponse {
    users: any[];
    total: number;
    query: string;
    offset: number;
    limit: number;
    processing_time_ms?: number;
}

export const searchAppleMusic = async ({queryKey}: {
    queryKey: [string, string, number?, number?]
}): Promise<Song[]> => {
    const [_key, searchTerm, offset = 0, limit = 20] = queryKey;
    try {
        const {data} = await api.get<MusicSearchResponse>(`/v1/search/music/apple`, {
            params: {
                query: searchTerm,
                offset,
                limit
            }
        });
        return data.results.songs.data;
    } catch (error) {
        throw error;
    }
};

export const searchUsers = async ({queryKey}: {
    queryKey: [string, SearchQueryParams]
}): Promise<UserSearchResponse> => {
    const [_key, params] = queryKey;
    const { query, offset = 0, limit = 20 } = params;

    try {
        const result = await api.get<UserSearchResponse>('/v1/search/users', {
            params: {
                query,
                offset,
                limit
            }
        });
        return result.data;
    } catch (error) {
        if (error.response) {
            console.error('Error:', error.response.data.detail);
            throw new Error(error.response.data.detail);
        } else {
            console.error('Error:', error.message);
            throw new Error(error.message);
        }
    }
};

