import {api} from "@/api/apiConfig";


export const getUserTrackBookmarks = async (): Promise<any> => {
    const result = await api.get('/v1/users/bookmarks/tracks');
    return result.data;
}


export const deleteUserTrackBookmark = async (bookmarkTrackId: string): Promise<any> => {
    const result = await api.delete(`/v1/users/bookmarks/tracks/${bookmarkTrackId}`);
    return result.data;
}


export const createUserTrackBookmark = async (songToPost: any): Promise<any> => {
    const result = await api.post('/v1/users/bookmarks/tracks', JSON.stringify(songToPost));
    return result.data;
}