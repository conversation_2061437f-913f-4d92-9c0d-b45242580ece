import {createPost} from "@/api/postsAPI";
import * as queryKeys from '@/constants/queryKeys';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {useRouter} from "expo-router";
import {useToastController} from "@tamagui/toast";



export const usePostSong = () => {
    const queryClient = useQueryClient();
    const toast = useToastController();
    const router = useRouter();

    return useMutation({
        mutationFn: async ({ songData, audioUri }: { songData: any, audioUri: string | null }) =>
            createPost(songData, audioUri),
        onError: (error: any) => {
            if (error.response?.status === 409) {
                toast.show('You already shared your banger Today!', {
                    message: "wait until tomorrow to share another banger",
                    theme: 'red',
                })
            } else {
                console.log("Error posting song:", error.message || error);
            }
        },
        onSuccess: () => {
            void queryClient.invalidateQueries({ queryKey: queryKeys.USER_FEED_QUERY_KEY });
            void queryClient.invalidateQueries({ queryKey: queryKeys.USER_BOOKMARK_QUERY_KEY });
            router.back();
            router.replace('/Home/HomeScreen');
        },
    });
}