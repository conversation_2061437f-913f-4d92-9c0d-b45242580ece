import { useMutation, UseMutationResult } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { authWithApple, authWithGoogle } from '../api/authAPI';


export const useAppleAuth = (): UseMutationResult<any, AxiosError, any> => {
    return useMutation<any, AxiosError, any>({
        mutationFn: authWithApple,
        onSuccess: (data) => {
            return data;
        },
        onError: (error: AxiosError) => {
            console.log('Authentication failed:', error.response?.data);
        },
    });
};



export const useGoogleAuth = (): UseMutationResult<any, AxiosError, any> => {
    return useMutation<any, AxiosError, any>({
        mutationFn: authWithGoogle,
        onSuccess: (data) => {
            return data;
        },
        onError: (error: AxiosError) => {
            console.log('Authentication failed:', error.response?.data);
        },
    });
};