// hooks/useHideTabBar.ts
import { useCallback } from 'react';
import { useNavigation, useFocusEffect } from 'expo-router';
import { StyleSheet } from 'react-native';

// Import shared tabBarStyles here or define them inline
// If you import from a shared file, remove the inline styles below
// and reference them from that import instead.

export function useHideTabBar() {
    const navigation = useNavigation();

    useFocusEffect(
        useCallback(() => {
            // Grab the parent navigator (the Tabs)
            const parent = navigation.getParent();

            // Hide the tab bar
            parent?.setOptions({ tabBarStyle: { display: 'none' } });

            // Automatically restore tab bar when unfocused
            return () => {
                parent?.setOptions({ tabBarStyle: styles.tabBar });
            };
        }, [navigation])
    );
}

// If you want to keep the style inline, you can do so.
// Or remove it here and import from a "tabBarStyles.ts" file.
const styles = StyleSheet.create({
    tabBar: {
        position: 'absolute',
        bottom: 16,
        left: 16,
        right: 16,
        height: 70,
        backgroundColor: 'rgba(0, 0, 0, 0.6)',
        borderRadius: 35,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 5,
        borderWidth: 0.5,
        borderColor: 'rgba(255, 255, 255, 0.5)',
    },
});