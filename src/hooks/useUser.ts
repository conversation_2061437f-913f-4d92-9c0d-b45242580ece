import {getUserData, getUserFeed} from "@/api/usersAPI";
import {useQuery} from "@tanstack/react-query";
import * as queryKeys from '@/constants/queryKeys';

type Post = {
    posterProfilePicture: string;
    artistName: string;
    trackName: string;
    postRating: number;
    posterUsername: string;
    artworkURL: string;
    audioPreviewURL: string;
    postID: string;
    spotifyURL?: string | undefined;
    trackURL?: string | undefined;
    postedAt: string;
    userReaction: "Like" | "Dislike" | null;
    comments: any[];
    audioCommentURL?: string | null;
};

type UserFeed = {
    posts: Post[];
};


export const useUserData = () => useQuery({
    queryKey: queryKeys.USER_DATA_QUERY_KEY,
    queryFn: getUserData,
});

export const useUserFeed = () => useQuery<UserFeed>({
    queryKey: queryKeys.USER_FEED_QUERY_KEY,
    queryFn: getUserFeed,
    refetchInterval: 30000,
});

// TODO: Check why I need this
export const prefetchUserFeed = async (queryClient: any) => {
    await queryClient.prefetchQuery({
        queryKey: queryKeys.USER_FEED_QUERY_KEY,
        queryFn: getUserFeed,
    })
}