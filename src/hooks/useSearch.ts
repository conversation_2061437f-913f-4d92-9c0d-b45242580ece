import { useQuery, UseQueryResult } from '@tanstack/react-query';
import { searchAppleMusic } from '../api/searchAPI';
import { Song } from '../types/MusicTypes';


export const useAppleMusicSearch = ({ searchTerm, isEnabled }: { searchTerm: string; isEnabled: boolean }): UseQueryResult<Song[], unknown> => {
    return useQuery({
      queryKey: ['musicSearch', searchTerm],
      queryFn: searchAppleMusic,
      enabled: isEnabled && searchTerm.length > 0,
      staleTime: 1000 * 60 * 5, 
    });
  };


//TODO: add friend search hook hier


