import React, { useRef, useEffect } from 'react';
import { Animated, PanResponder, Dimensions, View } from 'react-native';
import { XStack, YStack } from 'tamagui';
import MusicItem from '@/components/MusicItem';
import { Trash2 } from '@tamagui/lucide-icons';

const SWIPE_THRESHOLD = -80;
const MAX_SWIPE = -100;
const { width } = Dimensions.get('window');

interface BookmarkedSongProps {
    song: any;
    onDelete: (song: any) => void;
    handlePostSong: (song: any) => void;
}

const BookmarkedSong: React.FC<BookmarkedSongProps> = React.memo(({ song, onDelete, handlePostSong }) => {
    const swipeAnimation = useRef(new Animated.Value(0)).current;

    const panResponder = PanResponder.create({
        onMoveShouldSetPanResponder: (_, gestureState) => {
            const isHorizontalSwipe = Math.abs(gestureState.dx) > Math.abs(gestureState.dy * 2);
            const isLeftSwipe = gestureState.dx < 0;
            return isHorizontalSwipe && isLeftSwipe;
        },
        onPanResponderMove: (_, gestureState) => {
            const dx = gestureState.dx;
            const x = dx < 0 ? Math.max(MAX_SWIPE, dx * (1 - Math.abs(dx) / (width * 2))) : 0;
            swipeAnimation.setValue(x);
        },
        onPanResponderRelease: (_, gestureState) => {
            if (gestureState.dx < SWIPE_THRESHOLD) {
                Animated.spring(swipeAnimation, {
                    toValue: MAX_SWIPE,
                    useNativeDriver: true,
                    tension: 40,
                    friction: 7
                }).start();
                onDelete(song);
            } else {
                Animated.spring(swipeAnimation, {
                    toValue: 0,
                    useNativeDriver: true,
                    tension: 50,
                    friction: 7
                }).start();
            }
        },
        onPanResponderTerminate: () => {
            Animated.spring(swipeAnimation, {
                toValue: 0,
                useNativeDriver: true,
                tension: 50,
                friction: 7
            }).start();
        }
    });

    return (
        <View key={song.isrc}>
            <Animated.View
                style={{
                    transform: [{ translateX: swipeAnimation }],
                }}
                {...panResponder.panHandlers}
            >
                <XStack width="100%">
                    <YStack flex={1} marginLeft={-10}>
                        <MusicItem
                            song={{
                                id: song.isrc,
                                artwork: song.artworkURL,
                                artistName: song.artistName,
                                name: song.trackName,
                            }}
                            previewUrl={song.audioPreviewURL}
                            handlePostSong={() => handlePostSong(song)}
                        />
                    </YStack>
                    <Animated.View
                        style={{
                            position: 'absolute',
                            right: 0,
                            height: '100%',
                            justifyContent: 'center',
                            alignItems: 'flex-end',
                            backgroundColor: 'red',
                            padding: 30,
                            transform: [{
                                translateX: swipeAnimation.interpolate({
                                    inputRange: [MAX_SWIPE, 0],
                                    outputRange: [0, 60],
                                })
                            }],
                            opacity: swipeAnimation.interpolate({
                                inputRange: [MAX_SWIPE, MAX_SWIPE / 2, 0],
                                outputRange: [1, 1, 0]
                            })
                        }}
                    >
                        <Trash2 size={24} color="white" />
                    </Animated.View>
                </XStack>
            </Animated.View>
        </View>
    );
});

export default BookmarkedSong;
