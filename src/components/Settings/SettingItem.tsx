import React from 'react';
import {Button, Text, XStack} from 'tamagui';
import {MaterialIcons} from '@expo/vector-icons';

interface SettingItemProps {
    icon: React.ReactNode;
    title: string;
    onPress: () => void;
}

const SettingItem: React.FC<SettingItemProps> = ({icon, title, onPress}) => {
    return (
        <Button size="$5" onPress={onPress}>
            <XStack alignItems="center" justifyContent="space-between">
                <XStack alignItems="center" space="$3" style={{flex: 1}}>
                    {icon}
                    <Text>{title}</Text>
                </XStack>
                <MaterialIcons name="chevron-right" size={24} color="white"/>
            </XStack>
        </Button>
    );
};


export default SettingItem;
