import React from 'react';
import {Button, Text, XStack} from 'tamagui';
import {MaterialIcons} from '@expo/vector-icons';

interface SocialLinkProps {
    icon: React.ReactNode;
    title: string;
    onPress: () => void;
}

const SocialLink: React.FC<SocialLinkProps> = ({icon, title, onPress}) => {
    return (
        <Button size="$5" onPress={onPress}>
            <XStack alignItems="center" justifyContent="space-between">
                <XStack alignItems="center" space="$3" style={{flex: 1}}>
                    {icon}
                    <Text>{title}</Text>
                </XStack>
                <MaterialIcons name="chevron-right" size={24} color="white"/>
            </XStack>
        </Button>
    );
};


export default SocialLink;
