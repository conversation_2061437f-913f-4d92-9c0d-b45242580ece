import React, {useEffect, useState} from 'react';
import {Button, Card, Image, XStack, YStack} from 'tamagui';
import {Feather} from '@expo/vector-icons';
import {Bookmark} from "@tamagui/lucide-icons";
import {useMutation, useQuery, useQueryClient} from "@tanstack/react-query";
import {createUserTrackBookmark, deleteUserTrackBookmark, getUserTrackBookmarks} from "@/api/bookmarksAPI";
import {usePostStore} from "@/stores/postStore";
import {useToastController} from "@tamagui/toast";
import ConditionalMarqueeText from "@/components/FeedCard/ConditionalMarqueeText";
import {useMusicPlayerStore} from "@/stores/useMusicPlayerStore";

type MusicPlayerProps = {
    artworkURL: string | undefined;
    artistName: string | undefined;
    trackName: string | undefined;
    audioPreviewURL: string | undefined;
};

const MusicPlayer: React.FC<MusicPlayerProps> = ({
                                                     artworkURL,
                                                     artistName,
                                                     trackName,
                                                     audioPreviewURL,
                                                 }) => {

    const [isBookmarked, setIsBookmarked] = useState(false)
    const queryClient = useQueryClient();
    const songToPost = usePostStore((state) => state.songToPost);
    const toast = useToastController()
    const {playingUrl, isPlaying, loadAndPlayTrack, togglePlayPause} = useMusicPlayerStore();

    // TODO: redo bookmarking sending logic creating

    const getBookmarkTrackIdByIsrc = (isrc: string, userBookmarkTrack: any): string | null => {
        const songs = userBookmarkTrack?.bookmarks?.songs;
        if (!songs) return null;

        for (const song of songs) {
            if (song.isrc === isrc) {
                return song.bookmarkTrackId;
            }
        }
        return null;
    };


    const {mutate: addBookmark} = useMutation({
        mutationFn: ({songToPost}: { songToPost: any }) => createUserTrackBookmark(songToPost),
        onSuccess: () => {
            void queryClient.invalidateQueries({queryKey: ['userBookmarkTrack']});
        }
    });

    const {mutate: removeBookmark} = useMutation({
        mutationFn: (bookmarkTrackId: string) => deleteUserTrackBookmark(bookmarkTrackId),
        onSuccess: () => {
            void queryClient.invalidateQueries({queryKey: ['userBookmarkTrack']});
        }
    });

    const {data: userBookmarkTrack} = useQuery({
        queryKey: ['userBookmarkTrack'],
        queryFn: getUserTrackBookmarks,
    });


    useEffect(() => {
        if (userBookmarkTrack?.bookmarks?.songs && songToPost) {
            const isSongBookmarked = userBookmarkTrack?.bookmarks?.songs.some(
                (track: any) => track.isrc === songToPost?.isrc
            );
            setIsBookmarked(isSongBookmarked);
        }
    }, [userBookmarkTrack, songToPost]);


    const handleBookmark = async () => {
        if (songToPost) {
            console.log('songToPost:', songToPost); // Log the songToPost object

            if (isBookmarked) {
                const bookmarkTrackId = getBookmarkTrackIdByIsrc(songToPost.isrc, userBookmarkTrack);
                if (bookmarkTrackId) {
                    removeBookmark(bookmarkTrackId, {
                        onError: (error: any) => {
                            toast.show('Something went wrong!', {
                                message: "We are sorry, please try again later",
                                theme: 'red',
                            });
                            console.error('Error removing bookmark:', error);
                        },
                        onSuccess: () => {
                            setIsBookmarked(false);
                            void queryClient.invalidateQueries({queryKey: ['userBookmarkTrack']});
                            toast.show('Bookmark Removed!', {
                                message: "The song has been removed from your bookmarks",
                                theme: 'yellow',
                            });
                        },
                    });
                } else {
                    console.error('Bookmark Track ID not found for the given ISRC.');
                }
            } else {
                addBookmark({songToPost}, {
                    onError: (error: any) => {
                        toast.show('Something went wrong!', {
                            message: "We are sorry, please try again later",
                            theme: 'red',
                        });
                        console.error('Error adding bookmark:', error.response?.data);
                    },
                    onSuccess: () => {
                        setIsBookmarked(true);
                        void queryClient.invalidateQueries({queryKey: ['userBookmarkTrack']});
                        toast.show('Song Bookmarked!', {
                            message: "You can find it in your bookmarks",
                            theme: 'green',
                        });
                    },
                });
            }
        } else {
            console.error('songToPost is null');
        }
    };


    const isCurrentTrackPlaying = playingUrl === songToPost?.audioPreviewURL && isPlaying;

    const handleMusic = async () => {
        if (isCurrentTrackPlaying) {
            await togglePlayPause();
        } else {
            await loadAndPlayTrack(songToPost?.audioPreviewURL);
        }
    };


    return (
        <Card
            width={300}
            height={420}
            borderRadius={10}
        >
            <YStack flex={1} padding="$4" space="$4">
                <Image
                    source={{uri: artworkURL}}
                    width={250}
                    height={250}
                    alignSelf="center"
                />
                <XStack space="$2" alignItems="center" justifyContent="space-between">
                    <YStack>
                        <ConditionalMarqueeText
                            text={trackName || ''}
                            fontSize={18}
                            bold
                            color="$color"
                            width="85%"
                        />
                        <ConditionalMarqueeText
                            text={artistName || ''}
                            fontSize={14}
                            color="$color"
                            width="85%"
                        />
                    </YStack>
                    <Button
                        icon={<Bookmark size={18} color={isBookmarked ? 'black' : '$gray10'}/>}
                        backgroundColor={isBookmarked ? '#fed900' : 'black'}
                        color={isBookmarked ? 'black' : 'white'}
                        onPress={handleBookmark}
                        position="absolute"
                        top={1}
                        right={5}
                    >
                    </Button>
                </XStack>
                <XStack
                    backgroundColor="rgba(0, 0, 0, 0.5)"
                    borderRadius={18}
                    padding="$2"
                    alignItems="center"
                    justifyContent="center"
                >
                    <Button
                        onPress={handleMusic}
                        icon={<Feather name={isCurrentTrackPlaying ? 'pause' : 'play'} size={24} color="white"/>}
                        backgroundColor="transparent"
                        borderRadius={18}
                        size="$2.5"
                    >
                    </Button>

                </XStack>
            </YStack>
        </Card>
    );
};

export default MusicPlayer;