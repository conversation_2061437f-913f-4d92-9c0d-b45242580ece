import React from 'react';
import {useMusicPlayerStore} from '../../stores/useMusicPlayerStore';
import {But<PERSON>} from 'tamagui';
import {AntDesign} from '@expo/vector-icons';

export const MusicControl: React.FC = () => {
    const {togglePlayPause, isPlaying} = useMusicPlayerStore();

    return (
        <Button onPress={togglePlayPause}>
            <AntDesign name={isPlaying ? "pausecircleo" : "playcircleo"} size={30} color="white"/>
        </Button>
    );
};
