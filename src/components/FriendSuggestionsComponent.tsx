import React, { useState } from 'react';
import {ActivityIndicator, ScrollView, TouchableOpacity} from 'react-native';
import { Button, Avatar, Text, Card, YStack, XStack } from 'tamagui';
import { FontAwesome } from '@expo/vector-icons';
import {useMutation, useQuery, useQueryClient} from "@tanstack/react-query";
import {
    getUserSuggestedConnections,
    sendConnectionRequest,
} from "@/api/connectionsAPI";
import {useToastController} from "@tamagui/toast";


const FriendSuggestionsComponent: React.FC = () => {
    const queryClient = useQueryClient();
    const toast = useToastController();

    const {data: connectionSuggestions, isLoading, refetch} = useQuery({
        queryKey: ['userSuggestedFriend'],
        queryFn: getUserSuggestedConnections
    });

    const {mutateAsync} = useMutation({
        mutationFn: (RecipientUserID:any) => sendConnectionRequest(RecipientUserID),
        onSuccess: () => {
            void queryClient.invalidateQueries({queryKey: ['userSuggestedFriend']});
        }
    });

    const AddUser = async (RecipientUserID: string) => {
        try {
            await mutateAsync(RecipientUserID);
            toast.show('Request sent', {
                message: "",
                theme: 'green',
            });
        } catch (error: any) {
            if (error) {
                toast.show('Uhm we are sorry', {
                    message: "Something went wrong try again later.",
                    theme: 'red',
                });
            }
        }
    };


    if (isLoading) {
        return (
            <YStack flex={1} justifyContent="center" alignItems="center">
                <ActivityIndicator size="large" color="#fed900"/>
            </YStack>
        );
    }

    if (!connectionSuggestions || connectionSuggestions.length === 0) {
        return <Text>No new friend requests at the moment.</Text>;
    }


    return (
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <XStack space="$4">
                {connectionSuggestions.map((connectionSuggestion) => (
                    <Card key={connectionSuggestion.userID} padding="$5" width={250}>
                        <YStack alignItems="center">
                            <Avatar circular size="$8">
                                <Avatar.Image
                                    accessibilityLabel={"ProfilePicture"}
                                    src={connectionSuggestion.profilePicture || 'https://example.com/fallback-image.jpg'}
                                />
                                <Avatar.Fallback delayMs={600} backgroundColor="$yellow10" />
                            </Avatar>
                            <Text fontSize="$4" marginTop="$6">{connectionSuggestion.username}</Text>
                            <XStack space marginTop="$4">
                                <Button onPress={() => AddUser(connectionSuggestion.userID)}>
                                    Connect
                                </Button>
                            </XStack>
                        </YStack>
                    </Card>
                ))}
            </XStack>
        </ScrollView>
    );
};

export default FriendSuggestionsComponent;