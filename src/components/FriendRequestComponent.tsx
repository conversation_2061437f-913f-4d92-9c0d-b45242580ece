import React from 'react';
import {ActivityIndicator, ScrollView} from 'react-native';
import {<PERSON><PERSON>, <PERSON>ton, Card, Text, XStack, YStack} from 'tamagui';
import {getPendingConnectionRequests, updateConnectionRequest} from "@/api/connectionsAPI";
import {useMutation, useQuery, useQueryClient} from "@tanstack/react-query";
import {useToastController} from "@tamagui/toast";

type FriendRequest = {
    userID: string;
    username: string;
    firstName: string;
    lastName: string;
    profilePicture: string;
    friendRequestId: string;
};

type UpdateFriendRequestParams = {
    connectionRequestID: string;
    action: 'accepted' | 'rejected';
};

const FriendRequestComponent: React.FC = () => {
        const queryClient = useQueryClient();

        const {data: friendRequests, isLoading, refetch} = useQuery({
            queryKey: ['userFriendRequests'],
            queryFn: getPendingConnectionRequests
        });

        const toast = useToastController()

        const {mutateAsync} = useMutation({
            mutationFn: (params: UpdateFriendRequestParams) => updateConnectionRequest(params.connectionRequestID, params.action),
            onSuccess: () => {
                void queryClient.invalidateQueries({queryKey: ['userFriendRequests']});
            },
            onError: (error: any) => {
                if (error) {
                    toast.show('Uhm we are sorry', {
                        message: "Something went wrong try again later.",
                        theme: 'red',
                    });
                }
            }
        });

        const handleAcceptOrDeclineRequest = async (connectionRequestID: string, action: 'accepted' | 'rejected') => {
            await mutateAsync({connectionRequestID, action});
            if (action === 'accepted') {
                toast.show('Request Accepted', {
                    message: "",
                    theme: 'green',
                })
                void queryClient.invalidateQueries({queryKey: ['userLevel']});
                void queryClient.invalidateQueries({queryKey: ['userFeed']});
                void queryClient.invalidateQueries({queryKey: ['userConnections']});
            }
            if (action === 'rejected') {
                toast.show('Request Rejected', {
                    message: "",
                    theme: 'red',
                })
            }
        };

        if (isLoading) {
            return (
                <YStack flex={1} justifyContent="center" alignItems="center">
                    <ActivityIndicator size="large" color="#fed900"/>
                </YStack>
            );
        }

        if (!friendRequests || friendRequests.length === 0) {
            return <Text>No new friend requests at the moment.</Text>;
        }

        return (
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <XStack space="$4">
                    {friendRequests.map((request) => (
                        <Card key={request.userID} padding="$5" width={250}>
                            <YStack alignItems="center">
                                <Avatar circular size="$8">
                                    <Avatar.Image
                                        accessibilityLabel={request.username}
                                        src={request.profilePicture}
                                    />
                                    <Avatar.Fallback delayMs={600} backgroundColor="$blue10"/>
                                </Avatar>
                                <Text fontSize="$4" marginTop="$6">{request.username}</Text>
                                <Text fontSize="$2" marginTop="$1">{request.firstName} {request.lastName}</Text>
                                <XStack space marginTop="$4">
                                    <Button
                                        onPress={() => handleAcceptOrDeclineRequest(request.connectionRequestID, 'accepted')}>
                                        Accept
                                    </Button>
                                    <Button
                                        onPress={() => handleAcceptOrDeclineRequest(request.connectionRequestID, 'rejected')}>
                                        Decline
                                    </Button>
                                </XStack>
                            </YStack>
                        </Card>
                    ))}
                </XStack>
            </ScrollView>
        );
    }
;

export default FriendRequestComponent;