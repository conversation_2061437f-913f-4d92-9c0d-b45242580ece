import React, {useEffect, useState} from 'react';
import {Image, Stack, styled, Text, useTheme, XStack, YStack} from 'tamagui';
import Slider from '@react-native-community/slider';
import Animated, {Extrapolate, interpolate, useAnimatedStyle, useSharedValue,} from 'react-native-reanimated';

const AnimatedStack = Animated.createAnimatedComponent(Stack);
const AnimatedText = Animated.createAnimatedComponent(Text);

const SliderTrack = styled(YStack, {
    backgroundColor: '$gray2',
    borderRadius: 15,
    height: 50,
    marginVertical: '$4',
    overflow: 'visible',
    width: '100%',
    alignSelf: 'center',
});

const RatingNumber = styled(Text, {
    color: '$gray9',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
    variants: {
        active: {
            true: {
                color: '$primary',
                fontWeight: '700',
            },
        },
    },
});

export default function RatingSlider() {
    const theme = useTheme();
    const [rating, setRating] = useState(5);
    const [hasInteracted, setHasInteracted] = useState(false);
    const [sliderWidth, setSliderWidth] = useState(null);

    // Dummy data: other users' ratings with avatars
    const otherUsersRatings = [
        {userId: 1, rating: 5, avatar: 'https://via.placeholder.com/24/FF0000/FFFFFF?text=U1'},
        {userId: 2, rating: 7, avatar: 'https://via.placeholder.com/24/00FF00/FFFFFF?text=U2'},
        {userId: 3, rating: 5, avatar: 'https://via.placeholder.com/24/0000FF/FFFFFF?text=U3'},
        {userId: 4, rating: 5, avatar: 'https://via.placeholder.com/24/FFFF00/FFFFFF?text=U4'},
        {userId: 5, rating: 2, avatar: 'https://via.placeholder.com/24/FF00FF/FFFFFF?text=U5'},
        {userId: 6, rating: 10, avatar: 'https://via.placeholder.com/24/00FFFF/FFFFFF?text=U6'},
        // Add more dummy users as needed
    ];

    // Group avatars by rating
    const avatarsByRating = {};
    for (let i = 1; i <= 10; i++) {
        avatarsByRating[i] = [];
    }

    otherUsersRatings.forEach((userRating) => {
        avatarsByRating[userRating.rating].push(userRating.avatar);
    });

    const progressWidth = useSharedValue(((rating - 1) / 9) * 100);
    const bubblePosition = useSharedValue(((rating - 1) / 9) * 100);
    const bubbleScale = useSharedValue(1);

    useEffect(() => {
        if (sliderWidth !== null) {
            progressWidth.value = ((rating - 1) / 9) * 100;
            bubblePosition.value = ((rating - 1) / 9) * 100;
        }
    }, [sliderWidth]);

    const handleValueChange = (value) => {
        setRating(value);
        const newProgress = ((value - 1) / 9) * 100;

        progressWidth.value = newProgress;
        bubblePosition.value = newProgress;

        if (!hasInteracted) {
            setHasInteracted(true);
            bubbleScale.value = withSpring(1.1, {}, () => {
                bubbleScale.value = withSpring(1);
            });
        }
    };

    const progressStyle = useAnimatedStyle(() => ({
        width: `${progressWidth.value}%`,
    }));

    const bubbleStyle = useAnimatedStyle(() => {
        if (sliderWidth === null) {
            return {opacity: 0};
        }

        const bubbleWidth = 60; // Bubble width in pixels
        const maxLeft = sliderWidth - bubbleWidth;
        const bubbleLeft = interpolate(
            bubblePosition.value,
            [0, 100],
            [0, maxLeft],
            Extrapolate.CLAMP
        );

        return {
            left: bubbleLeft,
            transform: [{scale: bubbleScale.value}],
            opacity: 1,
        };
    });

    return (
        <YStack space="$4" width="100%" paddingHorizontal="$2" alignSelf="center">
            <SliderTrack
                onLayout={(event) => {
                    const {width} = event.nativeEvent.layout;
                    setSliderWidth(width);
                }}
            >
                {/* Slider Progress */}
                <AnimatedStack
                    position="absolute"
                    left={0}
                    top={0}
                    bottom={0}
                    backgroundColor="$primary"
                    borderRadius={15}
                    style={progressStyle}
                />

                {/* Slider Component */}
                <Slider
                    style={{width: '100%', height: '100%'}}
                    minimumValue={1}
                    maximumValue={10}
                    step={1}
                    value={rating}
                    onValueChange={handleValueChange}
                    minimumTrackTintColor="transparent"
                    maximumTrackTintColor="transparent"
                    thumbTintColor="transparent"
                />

                {/* Rating Numbers */}
                <XStack
                    position="absolute"
                    left={0}
                    right={0}
                    top={0}
                    bottom={0}
                    justifyContent="space-between"
                    alignItems="center"
                    paddingHorizontal="$3"
                >
                    {[...Array(10)].map((_, index) => (
                        <YStack key={index} flex={1} justifyContent="center" alignItems="center">
                            <RatingNumber active={rating === index + 1}>{index + 1}</RatingNumber>
                        </YStack>
                    ))}
                </XStack>

                {/* Bubble Indicator */}
                <AnimatedStack
                    position="absolute"
                    width={60}
                    height={60}
                    borderRadius={30}
                    backgroundColor="$background"
                    borderWidth={2}
                    borderColor={hasInteracted ? '$yellow10' : '$gray4'}
                    alignItems="center"
                    justifyContent="center"
                    top={-5} // Center over the slider
                    style={bubbleStyle}
                >
                    {hasInteracted ? (
                        <AnimatedText
                            fontSize="$5"
                            fontWeight="700"
                            color="$yellow10"
                            style={{transform: [{scale: bubbleScale.value}]}}
                        >
                            {rating}
                        </AnimatedText>
                    ) : (
                        <Text color="$yellow10" fontSize="$6" fontWeight="700">
                            ?
                        </Text>
                    )}
                </AnimatedStack>

                {/* User Avatars at the Bottom */}
                <XStack
                    position="absolute"
                    left={0}
                    right={0}
                    bottom={-40} // Position avatars below the slider track
                    justifyContent="space-between"
                    alignItems="flex-start"
                    paddingHorizontal="$3"
                >
                    {[...Array(10)].map((_, index) => {
                        const ratingValue = index + 1;
                        const avatars = avatarsByRating[ratingValue];
                        return (
                            <Stack key={index} flex={1} alignItems="center">
                                {avatars.slice(0, 3).map((avatarUrl, idx) => (
                                    <Image
                                        key={idx}
                                        source={{uri: avatarUrl}}
                                        style={{
                                            width: 24,
                                            height: 24,
                                            borderRadius: 12,
                                            marginTop: -8 * idx, // Stack avatars with overlap
                                            borderWidth: 1,
                                            borderColor: '#fff',
                                        }}
                                    />
                                ))}
                                {avatars.length > 3 && (
                                    <Text
                                        style={{
                                            fontSize: 12,
                                            color: theme.colors.gray9,
                                            marginTop: 4,
                                        }}
                                    >
                                        +{avatars.length - 3}
                                    </Text>
                                )}
                            </Stack>
                        );
                    })}
                </XStack>
            </SliderTrack>
        </YStack>
    );
}
