import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Button } from 'tamagui';
import { FontAwesome5, FontAwesome6 } from '@expo/vector-icons';
import { Image } from 'react-native';

const MusicProviderSelector = ({ musicProvider, setMusicProvider }) => {
    const providers = [
        {
            id: 'APPLE_MUSIC',
            icon: 'apple',
            color: '#FF0436',
            iconLibrary: FontAwesome5,
            type: 'font'
        },
        {
            id: 'SPOTIFY',
            icon: 'spotify',
            color: '#1DB954',
            iconLibrary: FontAwesome5,
            type: 'font'
        },
        {
            id: 'YOUTUBE_MUSIC',
            icon: require('@/assets/logos/youtube-music-logo.png'),
            color: '#F70000',
            type: 'image'
        },
        {
            id: 'NAPSTER',
            icon: 'napster',
            color: '#2159FF',
            iconLibrary: FontAwesome6,
            type: 'font'
        },
        {
            id: 'TIDAL',
            icon: require('@/assets/logos/tidal-logo.png'),
            color: '#757575',
            type: 'image'
        },
        {
            id: 'AMAZON_MUSIC',
            icon: require('@/assets/logos/amazon-music-logo.png'),
            color: '#23D1DA',
            iconLibrary: FontAwesome5,
            type: 'image'

        },
        {
            id: 'DEEZER',
            icon: require('@/assets/logos/deezer-logo.png'),
            color: '#A237FF',
            type: 'image'
        }
    ];

    const renderButton = (provider) => {
        return (
            <Button
                key={provider.id}
                size="$5"
                flex={1}
                backgroundColor={musicProvider === provider.id ? provider.color : '#000'}
                onPress={() => setMusicProvider(provider.id)}
                marginHorizontal="$1"
            >
                {provider.type === 'font' ? (
                    <provider.iconLibrary
                        name={provider.icon}
                        size={25}
                        color="white"
                    />
                ) : (
                    <Image source={provider.icon} style={{ width: 30, height: 30,   resizeMode: 'contain' }} />
                )}
            </Button>
        );
    };

    return (
        <YStack space="$2">
            {/* First Row - 3 buttons */}
            <XStack justifyContent="center" space="$2">
                {providers.slice(0, 3).map(renderButton)}
            </XStack>

            {/* Second Row - 4 buttons */}
            <XStack justifyContent="center" space="$2">
                {providers.slice(3, 7).map(renderButton)}
            </XStack>
        </YStack>
    );
};

export default MusicProviderSelector;
