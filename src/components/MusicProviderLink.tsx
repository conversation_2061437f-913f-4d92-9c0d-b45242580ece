import React from 'react';
import { But<PERSON> } from 'tamagui';
import FontAwesome5 from '@expo/vector-icons/FontAwesome5';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import { useQuery } from '@tanstack/react-query';
import { getUserData } from '@/api/usersAPI';
import { Link } from 'expo-router';
import { Image } from 'react-native';

interface MusicProviderLinkProps {
    trackURL?: string;
    size?: number;
}

const providers = [
    {
        id: 'APPLE_MUSIC',
        icon: 'apple',
        color: '#FF0436',
        iconLibrary: FontAwesome5,
        type: 'font'
    },
    {
        id: 'SPOTIFY',
        icon: 'spotify',
        color: '#1DB954',
        iconLibrary: FontAwesome5,
        type: 'font'
    },
    {
        id: 'YOUTUBE_MUSIC',
        icon: require('@/assets/logos/youtube-music-logo.png'),
        color: '#F70000',
        type: 'image'
    },
    {
        id: 'NAPSTER',
        icon: 'napster',
        color: '#2159FF',
        iconLibrary: FontAwesome6,
        type: 'font'
    },
    {
        id: 'TIDAL',
        icon: require('@/assets/logos/tidal-logo.png'),
        color: '#757575',
        type: 'image'
    },
    {
        id: 'AMAZON_MUSIC',
        icon: require('@/assets/logos/amazon-music-logo.png'),
        color: '#23D1DA',
        type: 'image'
    },
    {
        id: 'DEEZER',
        icon: require('@/assets/logos/deezer-logo.png'),
        color: '#A237FF',
        type: 'image'
    }
];

const MusicProviderLink: React.FC<MusicProviderLinkProps> = ({ trackURL, size = 24 }) => {
    const { data: userData, isLoading } = useQuery({
        queryKey: ['userData'],
        queryFn: getUserData,
    });

    if (!userData || !trackURL) {
        return null;
    }

    const provider = providers.find(p => p.id === userData.musicProvider);

    if (!provider) {
        return null;
    }

    return (
        <Link href={trackURL} asChild>
            <Button
                size="$3"
                circular
                borderRadius={50}
                backgroundColor={provider.color}
            >
                {provider.type === 'font' ? (
                    <provider.iconLibrary
                        name={provider.icon}
                        size={size}
                        color="white"
                    />
                ) : (
                    <Image 
                        source={provider.icon} 
                        style={{ 
                            width: size, 
                            height: size, 
                            resizeMode: 'contain' 
                        }} 
                    />
                )}
            </Button>
        </Link>
    );
};

export default MusicProviderLink;