import React from 'react';
import { ScrollView } from 'react-native';
import { YStack, Text, H6, Button, XStack, Avatar } from 'tamagui';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';

const FriendsSearchResult = ({ searchResults, onAddFriend, isLoading, isError }) => {
    return (
        <YStack flex={1} bg="$background">
            <H6 fontWeight="bold" mb="$4" marginLeft={"$4"} marginTop={"$4"}>Search Results ({searchResults.length})</H6>
            <ScrollView showsVerticalScrollIndicator={false}>
                {searchResults.map((result, index) => (
                    <XStack key={index} alignItems="center" justifyContent="space-between" padding={"$4"} borderBottomWidth={1} borderBottomColor={"$borderColor"}>
                        <XStack alignItems="center" flex={1}>
                            <Avatar circular size="$5" >
                                <Avatar.Image src={result.profilePicture} accessibilityLabel={result.username} />
                                <Avatar.Fallback delayMs={600} backgroundColor="#fed900" />
                            </Avatar>
                            <YStack marginLeft={"$4"} marginTop={10}>
                                <Text fontWeight="bold">{result.name}</Text>
                                <Text color={"$colorSubtitle"}>{result.username}</Text>
                            </YStack>
                        </XStack>
                            <Button onPress={() => onAddFriend(result.id)}>Add</Button>
                    </XStack>
                ))}
            </ScrollView>
        </YStack>
    );
};

export default FriendsSearchResult;
