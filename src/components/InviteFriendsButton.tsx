import React from 'react';
import { YStack, Text } from 'tamagui';
import { Ionicons } from '@expo/vector-icons';
import {TouchableOpacity} from 'react-native';
import * as Sharing from 'expo-sharing';

const onShare = async () => {
    try {
        const isAvailable = await Sharing.isAvailableAsync();
        if (!isAvailable) {
            alert('Sharing is not available on this platform');
            return;
        }

        await Sharing.shareAsync('https://wullup.com', {
            dialogTitle: 'Share this link',
        });
    } catch (error) {
        console.error('Error sharing link:', error);
    }
};

export default function InviteFriendsButton() {
    return (
        <YStack backgroundColor="#1c1c1e" borderRadius="$4" padding="$3" flexDirection="row" alignItems="center" justifyContent="space-between">
            <YStack>
                <Text fontWeight="bold" color="white" fontSize="$6">Invite Friends to Wullup!</Text>
                <Text color="#9e9e9e">https://wullup.com</Text>
            </YStack>
            <TouchableOpacity onPress={onShare}>
                <Ionicons name="share-outline" size={24} color="white" />
            </TouchableOpacity>
        </YStack>
    );
};


