import React, { useState } from 'react';
import {
    XStack,
    YStack,
    Text,
    Avatar,
    Button,
    Dialog,
    Adapt,
    Sheet,
} from 'tamagui';
import { X } from '@tamagui/lucide-icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getUserConnections, removeConnection } from '@/api/connectionsAPI';
import { ActivityIndicator, ScrollView, TouchableOpacity } from 'react-native';
import FontAwesome5 from '@expo/vector-icons/FontAwesome5';

interface Connection {
    connection_id: string;
    username: string;
    profilePicture: string;
}

export const ConnectionsDialog = () => {
    const queryClient = useQueryClient();
    const [dialogOpen, setDialogOpen] = useState(false);

    const { data: connections, isLoading } = useQuery({
        queryKey: ['userConnections'],
        queryFn: getUserConnections,
    });

    const removeMutation = useMutation({
        mutationFn: (connection_id: any) => removeConnection(connection_id),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['userConnections'] });
            queryClient.invalidateQueries({ queryKey: ['userLevel'] });
            queryClient.invalidateQueries({ queryKey: ['userFeed'] });
        },
    });

    const handleRemove = (connection: Connection) => {
        removeMutation.mutate(connection.connection_id);
    };

    return (
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <Dialog.Trigger asChild>
                <TouchableOpacity onPress={() => setDialogOpen(true)}>
                    <XStack alignItems="center" marginTop={10}>
                        <FontAwesome5 name="user-friends" size={20} color="white" />
                        <Text fontSize={16} color="#fff" marginLeft={8}>
                            {isLoading ? 'Loading...' : `${connections?.length || 0} Connections`}
                        </Text>
                    </XStack>
                </TouchableOpacity>
            </Dialog.Trigger>

            <Adapt when="sm" platform="touch">
                <Sheet modal dismissOnSnapToBottom onOpenChange={setDialogOpen}>
                    <Sheet.Frame padding="$4" gap="$4">
                        <Adapt.Contents />
                    </Sheet.Frame>
                    <Sheet.Overlay />
                </Sheet>
            </Adapt>

            {dialogOpen && (
                <Dialog.Content
                    bordered
                    elevate
                    animateOnly={['transform', 'opacity']}
                    animation={['quick', {
                        opacity: { overshootClamping: true },
                    }]}
                    enterStyle={{ x: 0, y: -20, opacity: 0, scale: 0.9 }}
                    exitStyle={{ x: 0, y: 10, opacity: 0, scale: 0.95 }}
                    gap="$4"
                    width="90%"
                    maxWidth={400}
                >
                    <ScrollView>
                        <Dialog.Title>Your Connections</Dialog.Title>
                        {isLoading ? (
                            <ActivityIndicator size="large" color="#3498db" />
                        ) : (
                            <YStack space="$2" maxHeight={400}>
                                {connections?.map((connection) => (
                                    <XStack
                                        key={connection.connection_id}
                                        alignItems="center"
                                        justifyContent="space-between"
                                        paddingVertical="$2"
                                    >
                                        <XStack alignItems="center" space="$2" flex={1}>
                                            <Avatar circular size="$4">
                                                <Avatar.Image src={connection.profilePicture} />
                                                <Avatar.Fallback backgroundColor="#3498db" />
                                            </Avatar>
                                            <Text fontSize="$2" fontWeight="bold">
                                                {connection.username}
                                            </Text>
                                        </XStack>
                                        <Button
                                            size="$2"
                                            icon={<X size={16} color="white" />}
                                            onPress={() => handleRemove(connection)}
                                            backgroundColor="$red5Dark"
                                        />
                                    </XStack>
                                ))}
                            </YStack>
                        )}
                    </ScrollView>
                </Dialog.Content>
            )}
        </Dialog>
    );
};