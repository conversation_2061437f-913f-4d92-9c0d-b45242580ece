import { useState, useRef } from 'react'
import { Image } from 'react-native'
import { XStack, YStack, Text, Stack } from 'tamagui'
import Animated, {
    useAnimatedStyle,
    withSpring,
    useSharedValue,
    runOnJS,
    useDerivedValue,
} from 'react-native-reanimated'
import {
    GestureDetector,
    Gesture,
    gestureHandlerRootHOC,
} from 'react-native-gesture-handler'
import { Medal, GripVertical } from '@tamagui/lucide-icons'

const ITEM_HEIGHT = 88
const SPRING_CONFIG = { damping: 20, stiffness: 200 }

type Song = {
    id: string
    cover: string
    songName: string
    artistName: string
} | null

const initialSongs: Song[] = [
    { id: '1', cover: '', songName: 'Bohemian Rhapsody', artistName: 'Queen' },
    { id: '2', cover: '', songName: 'Imagine', artistName: '<PERSON>' },
    { id: '3', cover: '', songName: '<PERSON>', artistName: '<PERSON>' },
    { id: '4', cover: '', songName: 'Like a Rolling Stone', artistName: '<PERSON>' },
    null,
    { id: '6', cover: '', songName: 'Smells Like Teen Spirit', artistName: '<PERSON>rvana' },
    { id: '7', cover: '', songName: 'Hey Jude', artistName: 'The Beatles' },
    null,
    { id: '9', cover: '', songName: "What's Going On", artistName: 'Marvin Gaye' },
    { id: '10', cover: '', songName: 'Respect', artistName: 'Aretha Franklin' },
]

// Helper to move an item in an array - now a worklet
function arrayMove<T>(array: T[], from: number, to: number): T[] {
    'worklet'
    const newArray = [...array]
    const [item] = newArray.splice(from, 1)
    newArray.splice(to, 0, item)
    return newArray
}

const AnimatedYStack = Animated.createAnimatedComponent(YStack)

type SortableItemProps = {
    song: Song
    itemKey: string
    order: Animated.SharedValue<string[]>
    containerHeight: number
    onDragEnd: () => void
}

const SortableItem = ({
                          song,
                          itemKey,
                          order,
                          containerHeight,
                          onDragEnd,
                      }: SortableItemProps) => {
    const isActive = useSharedValue(false)
    const dragOffset = useSharedValue(0)
    // Get the current index by looking up the key in the shared order
    const orderIndex = useDerivedValue(() => {
        return order.value.indexOf(itemKey)
    })
    // Calculate the target position based on the order index
    const targetY = useDerivedValue(() => {
        return orderIndex.value * ITEM_HEIGHT
    })
    // Record the starting Y position when drag begins
    const startY = useSharedValue(0)

    const gesture = Gesture.Pan()
        .onStart(() => {
            isActive.value = true
            startY.value = orderIndex.value * ITEM_HEIGHT
        })
        .onUpdate((event) => {
            dragOffset.value = event.translationY
            // Calculate where the dragged item currently is
            const currentY = startY.value + event.translationY
            // Determine its new order index
            const newIndex = Math.min(
                Math.max(Math.round(currentY / ITEM_HEIGHT), 0),
                order.value.length - 1
            )
            if (newIndex !== orderIndex.value) {
                // Update the shared order immediately
                order.value = arrayMove(order.value, orderIndex.value, newIndex)
            }
        })
        .onEnd(() => {
            // Snap the item back to its slot
            dragOffset.value = withSpring(0, SPRING_CONFIG)
            isActive.value = false
            runOnJS(onDragEnd)()
        })

    const animatedStyle = useAnimatedStyle(() => {
        // When active, use the drag offset; otherwise animate to the target position
        const translateY = isActive.value
            ? startY.value + dragOffset.value
            : withSpring(targetY.value, SPRING_CONFIG)
        return {
            position: 'absolute',
            left: 0,
            right: 0,
            transform: [{ translateY }],
            zIndex: isActive.value ? 999 : 1,
            shadowOpacity: withSpring(isActive.value ? 0.3 : 0),
            shadowRadius: withSpring(isActive.value ? 10 : 0),
            elevation: isActive.value ? 5 : 0,
        }
    })

    if (!song) {
        return (
            <AnimatedYStack
                style={animatedStyle}
                backgroundColor="$gray100"
                padding="$4"
                borderRadius="$4"
                marginBottom="$2"
                height={ITEM_HEIGHT}
                borderWidth={2}
                borderStyle="dashed"
                borderColor="$gray300"
                alignItems="center"
                justifyContent="center"
            >
                <Text color="$gray400">Empty slot</Text>
            </AnimatedYStack>
        )
    }

    return (
        <GestureDetector gesture={gesture}>
            <AnimatedYStack
                style={animatedStyle}
                backgroundColor="$background"
                padding="$4"
                borderRadius="$4"
                marginBottom="$2"
                height={ITEM_HEIGHT}
            >
                <XStack alignItems="center" space="$2">
                    <XStack backgroundColor="$gray100" padding="$2" borderRadius="$2" opacity={0.8}>
                        <GripVertical size={20} color="$gray900" />
                    </XStack>
                    <Image
                        source={{ uri: song.cover }}
                        style={{ width: 60, height: 60, borderRadius: 8, marginRight: 16 }}
                    />
                    <YStack flex={1}>
                        <Text fontSize="$4" fontWeight="600">
                            {song.songName}
                        </Text>
                        <Text fontSize="$2" color="$gray900">
                            {song.artistName}
                        </Text>
                    </YStack>
                </XStack>
            </AnimatedYStack>
        </GestureDetector>
    )
}

const RankNumber = ({ rank }: { rank: number }) => (
    <Stack
        width={48}
        height={ITEM_HEIGHT}
        backgroundColor="$gray100"
        borderRadius="$4"
        marginBottom="$2"
        alignItems="center"
        justifyContent="center"
    >
        {rank === 1 && <Medal color="$yellow10" size={24} />}
        {rank === 2 && <Medal color="$gray10" size={24} />}
        {rank === 3 && <Medal color="$orange10" size={24} />}
        {rank > 3 && <Text fontWeight="700" fontSize="$4">{rank}</Text>}
    </Stack>
)

const SortableSongList = gestureHandlerRootHOC(() => {
    const [songs, setSongs] = useState<Song[]>(initialSongs)
    // Initialize the shared order using unique keys
    const initialOrder = songs.map((song, index) => (song ? song.id : `empty-${index}`))
    const order = useSharedValue(initialOrder)
    const scrollRef = useRef<Animated.ScrollView>(null)

    const onDragEnd = () => {
        // When dragging ends, update the React state to reflect the new order.
        setSongs((prevSongs) => {
            const keyToSong = prevSongs.reduce<Record<string, Song>>((acc, song, index) => {
                const key = song ? song.id : `empty-${index}`
                acc[key] = song
                return acc
            }, {})
            return order.value.map((key) => keyToSong[key])
        })
    }

    return (
        <YStack padding="$4" maxWidth={800}>
            <Text textAlign="center" fontSize="$6" fontWeight="700" marginBottom="$4">
                Rank Your Top 10 Songs
            </Text>
            <XStack>
                <YStack width={64} marginRight="$4">
                    {order.value.map((_, index) => (
                        <RankNumber key={index} rank={index + 1} />
                    ))}
                </YStack>
                <YStack flex={1} style={{ height: ITEM_HEIGHT * songs.length }}>
                    <Animated.ScrollView
                        ref={scrollRef}
                        onScroll={(e) => {
                            // You can add auto-scroll logic if needed.
                        }}
                        scrollEventThrottle={16}
                        contentContainerStyle={{
                            height: ITEM_HEIGHT * songs.length,
                            position: 'relative',
                        }}
                    >
                        {songs.map((song, index) => {
                            const itemKey = song ? song.id : `empty-${index}`
                            return (
                                <SortableItem
                                    key={itemKey}
                                    song={song}
                                    itemKey={itemKey}
                                    order={order}
                                    containerHeight={ITEM_HEIGHT * songs.length}
                                    onDragEnd={onDragEnd}
                                />
                            )
                        })}
                    </Animated.ScrollView>
                </YStack>
            </XStack>
        </YStack>
    )
})

export default SortableSongList