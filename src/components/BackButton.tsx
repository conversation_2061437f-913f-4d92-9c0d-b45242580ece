import React from 'react'
import {useRouter} from 'expo-router'
import {But<PERSON>} from 'tamagui'
import MaterialIcons from '@expo/vector-icons/MaterialIcons';


const BackButton = () => {
    const router = useRouter()

    return (
        <Button
            onPress={() => router.back()}
            pressStyle={{borderWidth: 0}}
            padding={10}
            paddingTop={12}
            marginRight={10}
            chromeless>
            <MaterialIcons name="arrow-back-ios-new" size={20} color="white"/>
        </Button>
    )
}

export default BackButton
