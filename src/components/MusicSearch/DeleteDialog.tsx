import React from 'react';
import {Button, Dialog, XStack} from 'tamagui';

type DeleteDialogProps = {
    visible: boolean;
    onConfirm: () => void;
    onCancel: () => void;
};

const DeleteDialog: React.FC<DeleteDialogProps> = ({visible, onConfirm, onCancel}) => (
    <Dialog
        modal
        open={visible}
        onOpenChange={(open) => {
            if (!open) onCancel();
        }}
    >
        <Dialog.Portal>
            <Dialog.Overlay
                key="overlay"
                animation="quick"
                opacity={0.5}
                enterStyle={{opacity: 0}}
                exitStyle={{opacity: 0}}
            />
            <Dialog.Content
                bordered
                elevate
                key="content"
                animation={[
                    'quick',
                    {
                        opacity: {
                            overshootClamping: true,
                        },
                    },
                ]}
                enterStyle={{x: 0, y: -20, opacity: 0, scale: 0.9}}
                exitStyle={{x: 0, y: 10, opacity: 0, scale: 0.95}}
                space
            >
                <Dialog.Title>Remove Bookmark</Dialog.Title>
                <Dialog.Description>
                    Are you sure you want to remove this song from your bookmarks?
                </Dialog.Description>
                <XStack space="$3" justifyContent="flex-end">
                    <Button theme="alt1" onPress={onCancel}>
                        Cancel
                    </Button>
                    <Button color="$red10" onPress={onConfirm}>
                        Delete
                    </Button>
                </XStack>
            </Dialog.Content>
        </Dialog.Portal>
    </Dialog>
);

export default DeleteDialog;
