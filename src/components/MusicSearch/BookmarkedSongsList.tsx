import React from 'react';
import {ActivityIndicator, Animated} from 'react-native';
import {H4, Text, YStack} from 'tamagui';
import SwipeableBookmarkedItem from './SwipeableBookmarkedItem';

type BookmarkedSongsListProps = {
    bookmarks: any;
    swipeAnimations: { [key: string]: Animated.Value };
    onSwipeAction: (song: any) => void;
    onPostSong: (song: any) => void;
    isLoading: boolean;
    error: any;
};

const BookmarkedSongsList: React.FC<BookmarkedSongsListProps> = ({
                                                                     bookmarks,
                                                                     swipeAnimations,
                                                                     onSwipeAction,
                                                                     onPostSong,
                                                                     isLoading,
                                                                     error,
                                                                 }) => {
    if (isLoading) {
        return <ActivityIndicator size="large" color="#fed900"/>;
    }
    if (error) {
        return <Text>Sorry, something went wrong. We are working on it.</Text>;
    }
    return (
        <YStack marginHorizontal={10}>
            <H4>Bookmarked Bangers</H4>
            {Array.isArray(bookmarks?.songs) &&
                bookmarks.songs
                    .filter((song: any) => song.audioPreviewURL)
                    .sort(
                        (a: any, b: any) =>
                            new Date(b.bookmarkedAtDate).getTime() -
                            new Date(a.bookmarkedAtDate).getTime()
                    )
                    .map((song: any) => {
                        if (!swipeAnimations[song.isrc]) {
                            swipeAnimations[song.isrc] = new Animated.Value(0);
                        }
                        return (
                            <SwipeableBookmarkedItem
                                key={song.isrc}
                                song={song}
                                swipeAnimation={swipeAnimations[song.isrc]}
                                onSwipeAction={onSwipeAction}
                                onPostSong={onPostSong}
                            />
                        );
                    })}
        </YStack>
    );
};

export default BookmarkedSongsList;
