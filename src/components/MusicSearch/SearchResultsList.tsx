import React from 'react';
import {ActivityIndicator, ScrollView} from 'react-native';
import {Text, YStack} from 'tamagui';
import MusicItem from '@/components/MusicItem';

type SearchResultsListProps = {
    songs: any;
    isLoading: boolean;
    error: any;
    onPostSong: (song: any) => void;
};

const SearchResultsList: React.FC<SearchResultsListProps> = ({
                                                                 songs,
                                                                 isLoading,
                                                                 error,
                                                                 onPostSong,
                                                             }) => {
    if (isLoading) {
        return <ActivityIndicator size="large" color="#fed900"/>;
    }
    if (error) {
        return <Text>Couldn't find any track - try another query</Text>;
    }
    return (
        <ScrollView style={{flex: 1}}>
            {songs
                ?.filter((song: any) => song.attributes?.previews?.[0]?.url)
                .map((song: any) => (
                    <YStack key={song.id}>
                        <MusicItem
                            song={{id: song.id, ...song.attributes}}
                            previewUrl={song.attributes.previews[0].url}
                            handlePostSong={() => onPostSong(song)}
                        />
                    </YStack>
                ))}
        </ScrollView>
    );
};

export default SearchResultsList;
