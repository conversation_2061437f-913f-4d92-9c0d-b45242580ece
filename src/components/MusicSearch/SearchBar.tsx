import React from 'react';
import {Button, Input, YStack} from 'tamagui';

type SearchBarProps = {
    searchQuery: string;
    setSearchQuery: (text: string) => void;
    onCancel: () => void;
};

const SearchBar: React.FC<SearchBarProps> = ({searchQuery, setSearchQuery, onCancel}) => (
    <YStack
        flexDirection="row"
        marginBottom={25}
        marginHorizontal={10}
        marginTop={25}
        alignItems="center"
    >
        <Input
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Type a song name..."
            flex={1}
        />
        {searchQuery.length > 0 && (
            <Button marginLeft={10} onPress={onCancel}>
                Cancel
            </Button>
        )}
    </YStack>
);

export default SearchBar;
