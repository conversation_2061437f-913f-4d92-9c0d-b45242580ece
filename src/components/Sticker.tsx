import React from 'react';
import { StyleSheet, Image } from 'react-native';
import Animated, {
    useAnimatedStyle,
    useSharedValue,
} from 'react-native-reanimated';

import {
    Gesture,
    GestureDetector,
} from 'react-native-gesture-handler';

type StickerProps = {
    uri: string;
};

export function Sticker({ uri }: StickerProps) {
    /**
     * Shared values to store current transform
     */
    const translationX = useSharedValue(0);
    const translationY = useSharedValue(0);
    const scale = useSharedValue(1);
    const rotation = useSharedValue(0);

    /**
     * Shared values to store 'offset' - i.e. the last known transform
     * after the gesture ends, so we can continue from there
     */
    const offsetX = useSharedValue(0);
    const offsetY = useSharedValue(0);
    const offsetScale = useSharedValue(1);
    const offsetRotation = useSharedValue(0);

    /**
     * Pan Gesture (drag)
     */
    const panGesture = Gesture.Pan()
        .onStart(() => {
            // Start from the last offset
            translationX.value = offsetX.value;
            translationY.value = offsetY.value;
        })
        .onUpdate((e) => {
            // As we drag, update the translation
            translationX.value = offsetX.value + e.translationX;
            translationY.value = offsetY.value + e.translationY;
        })
        .onEnd(() => {
            // When the user releases, store the final translation into the offset
            offsetX.value = translationX.value;
            offsetY.value = translationY.value;
        });

    /**
     * Pinch Gesture (zoom in/out)
     */
    const pinchGesture = Gesture.Pinch()
        .onStart(() => {
            // Start from the last scale
            scale.value = offsetScale.value;
        })
        .onUpdate((e) => {
            // Apply pinch scaling factor to the last known scale
            scale.value = offsetScale.value * e.scale;
        })
        .onEnd(() => {
            // Save final scale into offset
            offsetScale.value = scale.value;
        });

    /**
     * Rotation Gesture (two-finger twist)
     */
    const rotationGesture = Gesture.Rotation()
        .onStart(() => {
            rotation.value = offsetRotation.value;
        })
        .onUpdate((e) => {
            // `e.rotation` is in radians, so we just add it
            rotation.value = offsetRotation.value + e.rotation;
        })
        .onEnd(() => {
            offsetRotation.value = rotation.value;
        });

    /**
     * Combine them so they can happen simultaneously
     */
    const composedGesture = Gesture.Simultaneous(
        panGesture,
        pinchGesture,
        rotationGesture
    );

    /**
     * Animated style applying all transforms
     */
    const animatedStyle = useAnimatedStyle(() => {
        return {
            transform: [
                { translateX: translationX.value },
                { translateY: translationY.value },
                // rotation is in radians, use `rotateZ()` with a string
                { rotateZ: `${rotation.value}rad` },
                { scale: scale.value },
            ],
        };
    });

    return (
        <GestureDetector gesture={composedGesture}>
            <Animated.View style={[styles.stickerContainer, animatedStyle]}>
                <Image
                    source={{ uri }}
                    style={styles.stickerImage}
                    resizeMode="contain"
                />
            </Animated.View>
        </GestureDetector>
    );
}

const styles = StyleSheet.create({
    stickerContainer: {
        position: 'absolute',
        width: 120,
        height: 120,
    },
    stickerImage: {
        width: '100%',
        height: '100%',
    },
});
