import React from 'react';
import { Modal, View } from 'react-native';
import { Button, Text, Card, YStack } from 'tamagui';

type ConfirmSignOutModalProps = {
    visible: boolean;
    onConfirm: () => void;
    onCancel: () => void;
};

const ConfirmSignOutModal: React.FC<ConfirmSignOutModalProps> = ({
                                                                     visible,
                                                                     onConfirm,
                                                                     onCancel,
                                                                 }) => {
    return (
        <Modal
            animationType="fade"
            transparent
            visible={visible}
            onRequestClose={onCancel}
        >
            {/* Dimmed backdrop */}
            <View
                style={{
                    flex: 1,
                    justifyContent: 'center',
                    backgroundColor: 'rgba(0,0,0,0.4)',
                }}
            >
                {/* Center the card */}
                <View
                    style={{
                        alignSelf: 'center',
                        width: '80%',
                        maxWidth: 400,
                    }}
                >
                    <Card
                        padding="$5"
                        borderRadius="$6"
                        backgroundColor="$background"
                        shadowColor="#000"
                        shadowOffset={{ width: 0, height: 4 }}
                        shadowOpacity={0.15}
                        shadowRadius={6}
                        elevation={5}
                    >
                        <YStack space="$3">
                            <Text
                                fontWeight="bold"
                                fontSize="$6"
                                textAlign="center"
                                marginBottom="$2"
                            >
                                Confirm Sign Out
                            </Text>
                            <Text
                                textAlign="center"
                                opacity={0.8}
                                marginBottom="$3"
                            >
                                Are you sure you want to sign out?
                            </Text>
                            <YStack space="$2">
                                <Button
                                    onPress={onConfirm}
                                    backgroundColor="$red6"
                                    borderRadius="$4"
                                    size="$5"
                                >
                                    Yes, Sign Out
                                </Button>
                                <Button
                                    onPress={onCancel}
                                    backgroundColor="$gray4"
                                    borderRadius="$4"
                                    size="$5"
                                >
                                    Cancel
                                </Button>
                            </YStack>
                        </YStack>
                    </Card>
                </View>
            </View>
        </Modal>
    );
};

export default ConfirmSignOutModal;