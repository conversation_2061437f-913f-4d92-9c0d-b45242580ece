import React from 'react';
import {Button, Text} from 'tamagui';
import {Ionicons} from '@expo/vector-icons';

interface AppleSignInButtonProps {
    onPress: () => void;
    disabled?: boolean; // Add disabled prop
}

const AppleSignInButton: React.FC<AppleSignInButtonProps> = ({onPress, disabled}) => {
    return (
        <Button
            width={350}
            height={55}
            backgroundColor={disabled ? "#d3d3d3" : "#000000"}
            borderColor="#ffffff"
            borderWidth={1}
            borderRadius={8}
            flexDirection="row"
            alignItems="center"
            justifyContent="center"
            onPress={onPress}
            disabled={disabled}
        >
            <Ionicons name="logo-apple" size={24} color="#ffffff" style={{marginRight: 10}}/>
            <Text color="#ffffff" fontSize={16} fontWeight="bold">
                Continue with Apple
            </Text>
        </Button>
    );
};

export default AppleSignInButton;
