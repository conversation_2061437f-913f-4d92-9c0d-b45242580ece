import React from 'react';
import {Button, Text} from 'tamagui';
import {AntDesign} from '@expo/vector-icons';

interface GoogleSignInButtonProps {
    onPress: () => void;
    disabled?: boolean; // Add disabled prop
}

const GoogleSignInButton: React.FC<GoogleSignInButtonProps> = ({onPress, disabled}) => {
    return (
        <Button
            width={350}
            height={55}
            backgroundColor={disabled ? "#d3d3d3" : "#4285F4"}  // Change color when disabled
            borderColor="#ffffff"
            borderWidth={1}
            borderRadius={4}
            flexDirection="row"
            alignItems="center"
            justifyContent="center"
            onPress={onPress}
            disabled={disabled}  // Disable button when disabled prop is true
        >
            <AntDesign name="google" size={24} color="#ffffff" style={{marginRight: 10}}/>
            <Text color="#ffffff" fontSize={16} fontWeight="bold">
                Continue with Google
            </Text>
        </Button>
    );
};

export default GoogleSignInButton;
