import React from "react";
import { Image, View, TouchableOpacity, useWindowDimensions, StyleSheet } from "react-native";
import { Button, H4, Paragraph, YStack } from 'tamagui';
import { AntDesign } from '@expo/vector-icons';
import { useMusicPlayerStore } from '../stores/useMusicPlayerStore';

interface Preview {
  url: string;
}

interface Artwork {
  url: string;
  width: number;
  height: number;
}

interface MusicItemProps {
  song: {
    id: string;
    artwork: string | Artwork;
    name: string;
    artistName: string;
    // previews: Preview[];
  };
  previewUrl: string;
  handlePostSong: () => void;
}

const MusicItem: React.FC<MusicItemProps> = ({ song, previewUrl, handlePostSong }) => {
  const { artwork, name, artistName } = song;
  const { playingUrl, isPlaying, loadAndPlayTrack, togglePlayPause } = useMusicPlayerStore();
  const isCurrentPlaying = playingUrl === previewUrl && isPlaying;

  const handlePlayPause = async () => {
    if (isCurrentPlaying) {
      await togglePlayPause();
    } else {
      await loadAndPlayTrack(previewUrl);
    }
  };

  const { width } = useWindowDimensions();
  const fontSize = width < 350 ? 14 : width < 500 ? 18 : 22;

  let artworkURL;
  if (typeof artwork === 'string') {
    artworkURL = artwork;
  }
    else {
        artworkURL = artwork.url.replace('{w}', '300').replace('{h}', '300');
    }

  return (
      <TouchableOpacity onPress={handlePostSong} style={styles.container}>
        <YStack flexDirection="row" alignItems="center" padding={15} space>
          <Image
              source={{ uri: artworkURL }}
              style={{ width: 100, height: 100, marginRight: 10 }}
          />
          <View style={styles.textContainer}>
            <H4 style={[styles.text, { fontSize }]} numberOfLines={2} ellipsizeMode="tail">{name}</H4>
            <Paragraph theme="alt2" style={styles.text} numberOfLines={1} ellipsizeMode="tail">{artistName}</Paragraph>
          </View>
        </YStack>
      </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexShrink: 1,
  },
  textContainer: {
    flexShrink: 1,
  },
  text: {
    flexShrink: 1,
  }
});

export default MusicItem;