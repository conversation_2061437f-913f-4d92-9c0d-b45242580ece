import React, {useEffect, useRef, useState} from "react";
import {<PERSON><PERSON>, <PERSON>ack, Text, XStack, YStack} from "tamagui";
import {Mic, PlayCircle, StopCircle, Trash2} from "@tamagui/lucide-icons";
import {Audio} from "expo-av";

export interface AudioRecorderProps {
    onFileChange: (uri: string | null) => void;
}

const AudioRecorder = ({onFileChange}: AudioRecorderProps) => {
    const [recording, setRecording] = useState<Audio.Recording | null>(null);
    const [sound, setSound] = useState<Audio.Sound | null>(null);
    const [status, setStatus] = useState<"idle" | "recording" | "playing">("idle");
    const [recordingUri, setRecordingUri] = useState<string | null>(null);
    const [durationMillis, setDurationMillis] = useState(0);
    const [currentPositionMillis, setCurrentPositionMillis] = useState(0);

    const intervalRef = useRef<NodeJS.Timer | null>(null);

    useEffect(() => {
        return () => {
            cleanup();
        };
    }, []);

    const cleanup = async () => {
        if (recording) {
            await recording.stopAndUnloadAsync();
        }
        if (sound) {
            await sound.unloadAsync();
        }
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
        }
    };

    const formatDuration = (millis: number) => {
        const totalSeconds = Math.floor(millis / 1000);
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        return `${minutes}:${seconds.toString().padStart(2, "0")}`;
    };

    const startRecording = async () => {
        try {
            const permission = await Audio.requestPermissionsAsync();
            if (!permission.granted) {
                alert("Permission to access microphone is required!");
                return;
            }

            await Audio.setAudioModeAsync({
                allowsRecordingIOS: true,
                playsInSilentModeIOS: true,
            });

            const recordingObject = new Audio.Recording();
            await recordingObject.prepareToRecordAsync(
                Audio.RecordingOptionsPresets.HIGH_QUALITY
            );
            await recordingObject.startAsync();

            setRecording(recordingObject);
            setStatus("recording");
            setDurationMillis(0);

            intervalRef.current = setInterval(async () => {
                const status = await recordingObject.getStatusAsync();
                setDurationMillis(status.durationMillis || 0);
            }, 500);
        } catch (error) {
            console.error("Failed to start recording", error);
        }
    };

    const stopRecording = async () => {
        try {
            if (!recording) return;

            await recording.stopAndUnloadAsync();
            const uri = recording.getURI();
            setRecordingUri(uri);
            setStatus("idle");

            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }

            if (uri) {
                onFileChange(uri);
                const {sound: newSound} = await Audio.Sound.createAsync(
                    {uri},
                    {shouldPlay: false},
                    onPlaybackStatusUpdate
                );
                setSound(newSound);
            }
        } catch (error) {
            console.error("Failed to stop recording", error);
        } finally {
            setRecording(null);
        }
    };

    const onPlaybackStatusUpdate = (
        playbackStatus: Audio.AVPlaybackStatus
    ) => {
        if (!playbackStatus.isLoaded) {
            if (playbackStatus.error) {
                console.error(`Playback Error: ${playbackStatus.error}`);
            }
        } else {
            setCurrentPositionMillis(playbackStatus.positionMillis || 0);
            if (playbackStatus.didJustFinish) {
                setStatus("idle");
                setCurrentPositionMillis(0);
            }
        }
    };

    const togglePlayback = async () => {
        if (!sound) return;

        try {
            if (status === "playing") {
                await sound.pauseAsync();
                setStatus("idle");
            } else {
                await sound.playFromPositionAsync(currentPositionMillis);
                setStatus("playing");
            }
        } catch (error) {
            console.error("Failed to toggle playback", error);
        }
    };

    const deleteRecording = async () => {
        try {
            if (sound) {
                await sound.unloadAsync();
            }
            setSound(null);
            setRecordingUri(null);
            setDurationMillis(0);
            setCurrentPositionMillis(0);
            onFileChange(null);
            setStatus("idle");
        } catch (error) {
            console.error("Failed to delete recording", error);
        }
    };

    const buttonSize = status === "idle" && recordingUri ? "$6" : "$8";

    const buttonBackgroundColor =
        status === "recording"
            ? "$red10"
            : status === "playing"
                ? "$green10"
                : status === "idle" && recordingUri
                    ? "$blue10"
                    : "#fed900";

    const buttonIcon =
        status === "recording"
            ? StopCircle
            : status === "playing"
                ? StopCircle
                : status === "idle" && recordingUri
                    ? PlayCircle
                    : Mic;

    const handleButtonPress =
        status === "recording"
            ? stopRecording
            : status === "playing"
                ? togglePlayback
                : status === "idle" && recordingUri
                    ? togglePlayback
                    : startRecording;

    const buttonAccessibilityLabel =
        status === "recording"
            ? "Stop Recording"
            : status === "playing"
                ? "Stop Playback"
                : status === "idle" && recordingUri
                    ? "Play Recording"
                    : "Start Recording";

    const displayDuration = () => {
        if (status === "recording") {
            return `Recording... ${formatDuration(durationMillis)}`;
        } else if (status === "playing") {
            const remainingMillis = durationMillis - currentPositionMillis;
            return `Playing... ${formatDuration(remainingMillis)}`;
        } else if (status === "idle" && recordingUri) {
            return `Ready to Play (${formatDuration(durationMillis)})`;
        } else {
            return "Tap to Record";
        }
    };

    return (
        <YStack space="$4" alignItems="center" width="100%" padding="$4">
            <Stack alignItems="center" justifyContent="center">
                <XStack space={25}>
                    <Button
                        size={buttonSize}
                        circular
                        icon={buttonIcon}
                        onPress={handleButtonPress}
                        backgroundColor={buttonBackgroundColor}
                        color="white"
                        accessibilityLabel={buttonAccessibilityLabel}
                        animation="bouncy"
                        pressStyle={{scale: 0.95}}
                    />
                    {recordingUri && (
                        <Button
                            icon={Trash2}
                            onPress={deleteRecording}
                            backgroundColor="$red10"
                            color="white"
                            size="$5"
                            marginTop="$2"
                            accessibilityLabel="Delete Recording"
                        />
                    )}
                </XStack>
                <Text fontSize="$6" marginTop="$2" color="$gray12">
                    {displayDuration()}
                </Text>
            </Stack>
        </YStack>
    );
};

export default AudioRecorder;