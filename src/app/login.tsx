import {Anchor, Checkbox, CheckedState, Image, Label, Text, XStack, YStack} from "tamagui";
import React, {useCallback, useEffect, useState} from "react";
import * as AppleAuthentication from "expo-apple-authentication";
import {useAuth} from "@/provider/AuthProvider";
import {Platform} from 'react-native';
import AppleSignInButton from "@/components/AuthButtons/AppleSignInButton";
import * as WebBrowser from "expo-web-browser";
import GoogleSignInButton from "@/components/AuthButtons/GoogleSignInButton";
import Constants from 'expo-constants';
import {Check as CheckIcon} from '@tamagui/lucide-icons';

WebBrowser.maybeCompleteAuthSession();

// Mock implementation for Expo Go
const mockGoogleSignIn = {
    configure: () => {
    },
    hasPlayServices: async () => true,
    signIn: async () => ({
        user: {
            email: '<EMAIL>',
            id: '12345',
            givenName: 'Test',
            familyName: 'User',
        }
    }),
};

// Use the real implementation if not in Expo Go, otherwise use the mock
const GoogleSignin = Constants.appOwnership === 'expo' ? mockGoogleSignIn : require("@react-native-google-signin/google-signin").GoogleSignin;

export default function LoginScreen() {
    const {onAppleOAuth, onGoogleOAuth, onDemoAuth} = useAuth();
    const [isChecked, setIsChecked] = useState<boolean>(false);

    const configureGoogleSignIn = () => {
        if (Constants.appOwnership !== 'expo') {
            GoogleSignin.configure({
                webClientId: "429511310888-4itbn380dd3jhvd4ag8deqiorh0hbmrs.apps.googleusercontent.com",
                iosClientId: "429511310888-797d5e4dkqv9p8timfh321abj5hd8c0a.apps.googleusercontent.com",
            });
        }
    };

    useEffect(() => {
        configureGoogleSignIn();
    }, []);

    const handleGoogleOAuth = async () => {
        try {
            await GoogleSignin.hasPlayServices();
            const userInfo = await GoogleSignin.signIn();

            console.log(userInfo);

            await onGoogleOAuth!(userInfo);

        } catch (e) {
            console.error("Error during Google Sign-In", e);
        }
    };

    const handleAppleOAuth = async () => {
        try {
            const appleCredential = await AppleAuthentication.signInAsync({
                requestedScopes: [
                    AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
                    AppleAuthentication.AppleAuthenticationScope.EMAIL,
                ],
            });

            await onAppleOAuth!(appleCredential);

        } catch (e) {
            console.log("Apple Login error:", e);
        }
    };

    const handleCheckedChange = useCallback((checked: CheckedState) => {
        setIsChecked(checked === true);
    }, []);

    return (
        <YStack
            flex={1}
            justifyContent="center"
            alignItems="center"
            gap="$10"
            style={{backgroundColor: "black"}}
        >
            <YStack
                onPress={async () => await onDemoAuth!()}
                alignItems="center"
                style={{
                    cursor: 'pointer',
                    width: 250,
                    height: 250,
                    marginTop: "-25%",
                }}
            >
                <Image
                    resizeMode="contain"
                    source={{
                        uri: require("../assets/images/wulluploginlogo.jpg"),
                        width: 50,
                        height: 50,
                    }}
                    style={{
                        width: 250,
                        height: 250,
                    }}
                />
            </YStack>

            <YStack position="absolute" bottom="$10" gap="$3">
                {Platform.OS !== 'android' && (
                    <AppleSignInButton onPress={handleAppleOAuth} disabled={!isChecked}/>
                )}

                <GoogleSignInButton onPress={handleGoogleOAuth} disabled={!isChecked}/>

                <XStack width={300} alignItems="center" gap="$2" style={{marginLeft: 15}}>
                    <Checkbox
                        size={'$5'}
                        checked={isChecked}
                        onCheckedChange={handleCheckedChange}
                    >
                        <Checkbox.Indicator>
                            <CheckIcon/>
                        </Checkbox.Indicator>
                    </Checkbox>

                    <Label size={'$1'} style={{marginTop: 5}}>
                        I read and accept the
                        <Text> </Text>
                        <Anchor
                            href="https://www.wullup.com/privacy"
                            target="_blank"
                            size={'$1'}
                            color="$yellow10"
                            textDecorationLine="underline"
                            fontWeight="bold"
                        >
                            Terms of Use
                        </Anchor>
                        <Text> </Text>
                        and
                        <Text> </Text>
                        <Anchor
                            href="https://www.wullup.com/privacy"
                            target="_blank"
                            size={'$1'}
                            color="$yellow10"
                            textDecorationLine="underline"
                            fontWeight="bold"
                        >
                            Privacy Policy
                        </Anchor>
                    </Label>
                </XStack>
            </YStack>
        </YStack>
    );
}