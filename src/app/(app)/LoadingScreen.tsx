import React, { memo, useMemo, useState } from "react";
import { TextProps, View, StatusBar } from "react-native";
import Animated, {
    FadeOut,
    runOnJS,
    SlideInDown,
} from "react-native-reanimated";
import {router} from "expo-router";
import {useQueryClient} from "@tanstack/react-query";
import { prefetchUserFeed, useUserData } from "@/hooks/useUser";
import {usePostHog} from "posthog-react-native";

type AnimatedSentenceProps = TextProps & {
    onExitFinish?: () => void;
    onEnterFinish?: (wordsCount: number) => void;
    stagger?: number;
};

//
// 2) Helper to simulate waiting
//
async function wait(ms: number) {
    return new Promise((resolve) => setTimeout(resolve, ms));
}


const staticColor = {
    background: "#000000",
    text: "#FED900",
};


const randomFacts = [
    '<PERSON> used to be a backup dancer for Notorious B.I.G.',
    '<PERSON><PERSON> and <PERSON> attended the same high school.',
    '<PERSON> once went by the name <PERSON><PERSON> <PERSON>.',
    '<PERSON><PERSON><PERSON> E’s Ruth<PERSON> record signed the Black-Eyed Peas.',
    '<PERSON><PERSON> was on the set of Tupac’s California Love Video.',
    'Notorious B.I.G wanted to open a soul food restaurant called Big Poppa’s.',
    '<PERSON> Z wrote his ‘The Blueprint’ album in two days.',
    'Kanye West lived in China for a year when he was ten.',
    'The Game used to be one of the best Madden players in the world.',
    'Snoop and Cameron Diaz went to the same High School.',
];

const AnimatedSentence = memo(
    ({
         children,
         onExitFinish,
         onEnterFinish,
         stagger = 100,
         ...rest
     }: AnimatedSentenceProps) => {
        if (typeof children !== "string") {
            throw new Error("AnimatedSentence only accepts a string.");
        }

        const words = useMemo(() => children.split(" "), [children]);

        return (
            <View
                style={{ flexDirection: "row", flexWrap: "wrap", gap: 4 }}
                key={children}
            >
                {words.map((word, index) => (
                    <View style={{ overflow: "hidden" }} key={`word-${index}`}>
                        <Animated.Text
                            entering={SlideInDown.springify()
                                .damping(80)
                                .stiffness(200)
                                .delay(index * stagger)
                                .withInitialValues({
                                    // The drop-down distance
                                    originY: ((rest.style?.fontSize as number) ?? 50) * 2,
                                })
                                .withCallback((finished) => {
                                    if (
                                        finished &&
                                        index === words.length - 1 &&
                                        onEnterFinish &&
                                        children !== ""
                                    ) {
                                        runOnJS(onEnterFinish)(words.length);
                                    }
                                })}
                            exiting={FadeOut.springify()
                                .damping(80)
                                .stiffness(200)
                                .withCallback((finished) => {
                                    if (
                                        finished &&
                                        index === words.length - 1 &&
                                        onExitFinish &&
                                        children !== ""
                                    ) {
                                        runOnJS(onExitFinish)();
                                    }
                                })}
                            {...rest}
                        >
                            {word}
                        </Animated.Text>
                    </View>
                ))}
            </View>
        );
    }
);


export default function LoadingScreen() {
    // Start prefetching the data
    const queryClient = useQueryClient();

    void prefetchUserFeed(queryClient)

    const {data: userData, isLoading: isUserDataLoading, isError: isUserDataError, isSuccess: isUserDataSuccess} = useUserData();

    const posthog = usePostHog();

    React.useEffect(() => {
        if (userData && posthog) {
            posthog.identify(userData.data.id, {
                email: userData.data.email,
                username: userData.data.username,
            });
        }
    }, [userData, posthog]);


    const [sentence, setSentence] = useState(randomFacts[Math.floor(Math.random() * randomFacts.length)]);

    // Called after the last word has exited
    const onExitFinish = async () => {
        router.replace("/(app)/(tabs)/Home/HomeScreen");
    };

    // Called after the last word has entered
    const onEnterFinish = async (wordsCount: number) => {
        // ~0.3 seconds per word (avg ~200 wpm)
        await wait((wordsCount * 60 * 1000) / 200);

        // Clear sentence to trigger exit animation
        setSentence("");
    };

    return (
        <Animated.View
            style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                padding: 20,
                backgroundColor: staticColor.background,
            }}
        >
            <AnimatedSentence
                style={{
                    fontSize: 52,
                    lineHeight: 52,
                    letterSpacing: -2,
                    fontWeight: "900",
                    flexShrink: 1,
                    color: staticColor.text,
                    textTransform: "uppercase",
                }}
                onExitFinish={onExitFinish}
                onEnterFinish={onEnterFinish}
            >
                {sentence}
            </AnimatedSentence>
            <StatusBar hidden />
        </Animated.View>
    );
}
