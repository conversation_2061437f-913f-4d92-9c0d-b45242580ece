import { <PERSON>, Stack, router } from 'expo-router';
import React, { useState } from 'react';
import { SafeAreaView, StyleSheet } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { StatusBar } from 'expo-status-bar';
import {
    GestureDetector,
    Gesture,
    Directions,
} from 'react-native-gesture-handler';
import Animated, {
    FadeIn,
    FadeOut,
    SlideOutLeft,
    SlideInRight,
} from 'react-native-reanimated';
import { YStack, XStack, Theme, Button, Text } from 'tamagui';

const onboardingSteps = [
    {
        icon: 'share-alt',
        title: 'Cross-Platform Sharing',
        description: 'Easily share your favorite music with your friends across Spotify, Apple Music and more coming soon!',
    },
    {
        icon: 'slideshare',
        title: 'Share Your Bangers',
        description: 'Share your bangers with your friends and find out who has the best taste in music.',
    },
    {
        icon: 'comments',
        title: 'Your Wish is our command!',
        description:
            'Help us build the ultimate music platform for enthusiasts. Join our WhatsApp group and share your ideas and feedback.',
    },
];

export default function OnboardingScreen() {
    const [screenIndex, setScreenIndex] = useState(0);

    const data = onboardingSteps[screenIndex];

    const onContinue = () => {
        const isLastScreen = screenIndex === onboardingSteps.length - 1;
        if (isLastScreen) {
            endOnboarding();
        } else {
            setScreenIndex(screenIndex + 1);
        }
    };

    const onBack = () => {
        const isFirstScreen = screenIndex === 0;
        if (isFirstScreen) {
            endOnboarding();
        } else {
            setScreenIndex(screenIndex - 1);
        }
    };

    const endOnboarding = () => {
        router.replace("/(onboarding)/PushNotificationOptInScreen");
    };

    const swipes = Gesture.Simultaneous(
        Gesture.Fling().direction(Directions.LEFT).onEnd(onContinue),
        Gesture.Fling().direction(Directions.RIGHT).onEnd(onBack)
    );

    return (
            <SafeAreaView style={{ flex: 1 }}>
                <Stack.Screen options={{ headerShown: false }} />
                <StatusBar style="light" />
                <YStack flex={1} justifyContent="center" padding="$4">
                    <XStack gap="$2" marginHorizontal="$3">
                        {onboardingSteps.map((step, index) => (
                            <YStack
                                key={index}
                                flex={1}
                                height={3}
                                backgroundColor={index === screenIndex ? '#ffd900' : 'grey'}
                            />
                        ))}
                    </XStack>

                    <GestureDetector gesture={swipes}>
                        <YStack flex={1} key={screenIndex} justifyContent="center">
                            <Animated.View entering={FadeIn} exiting={FadeOut}>
                                <FontAwesome5
                                    style={{ alignSelf: 'center', margin: 20, marginTop: 70 }}
                                    name={data.icon}
                                    size={250}
                                    color="#ffd900"
                                />
                            </Animated.View>

                            <YStack marginTop="auto">
                                <Animated.Text
                                    entering={SlideInRight}
                                    exiting={SlideOutLeft}
                                    style={styles.title}
                                >
                                    {data.title}
                                </Animated.Text>
                                <Animated.Text
                                    entering={SlideInRight.delay(50)}
                                    exiting={SlideOutLeft}
                                    style={styles.description}
                                >
                                    {data.description}
                                </Animated.Text>

                                <XStack marginTop="$3" alignItems="center" gap="$2">
                                    <Button onPress={endOnboarding} backgroundColor="transparent">
                                        Skip
                                    </Button>
                                    <Button onPress={onContinue} flex={1}>
                                        Continue
                                    </Button>
                                </XStack>
                            </YStack>
                        </YStack>
                    </GestureDetector>
                </YStack>
            </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    title: {
        color: '#FDFDFD',
        fontSize: 50,
        fontFamily: 'InterBlack',
        letterSpacing: 1.3,
    },
    description: {
        color: 'gray',
        fontSize: 20,
        fontFamily: 'Inter',
        lineHeight: 28,
        marginVertical: 25,
    },
});
