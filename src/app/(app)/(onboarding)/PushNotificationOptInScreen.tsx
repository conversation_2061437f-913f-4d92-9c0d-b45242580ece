import React, { useState, useEffect } from 'react';
import { Alert, Platform } from 'react-native';
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import Constants from 'expo-constants';
import { YStack, Text, Button, XStack } from 'tamagui';
import {updatePNToken} from "@/api/usersAPI";
import {router} from "expo-router";

// Set up the notification handler
Notifications.setNotificationHandler({
    handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: false,
        shouldSetBadge: false,
    }),
});

const NotificationPermissionScreen = () => {
    const [expoPushToken, setExpoPushToken] = useState<string | undefined>(undefined);
    const [permissionStatus, setPermissionStatus] = useState<string>('unknown');

    useEffect(() => {
        registerForPushNotificationsAsync().then(token => {
            if (token) {
                setExpoPushToken(token);
                setPermissionStatus('granted');
            }
        });
    }, []);

    const requestPermission = async () => {
        const token = await registerForPushNotificationsAsync();
        if (token) {
            setExpoPushToken(token);
            setPermissionStatus('granted');
            await updatePNToken(token);
            router.replace("/(tabs)/Home/HomeScreen");
        } else {
            setPermissionStatus('denied');
            Alert.alert(
                'Permission not granted',
                'Allow notifications to receive important updates.'
            );
        }
    };


    return (
        <YStack flex={1} justifyContent="center" alignItems="center" padding="$4">
            <Text fontSize="$6" fontWeight="bold" marginBottom="$4">
                Enable Push Notifications
            </Text>
            <Text fontSize="$4" textAlign="center" marginBottom="$6">
                Don't miss a beat - enable notifications to stay connected with friends and catch the latest updates instantly.</Text>
            <Button
                size="$5"
                width="75%"
                onPress={requestPermission}
                marginBottom="$7"
                backgroundColor="#fed900"
                color={"black"}
            >
                Allow Notifications
            </Button>
            <Button size="$4" width="75%" onPress={() => router.replace("/(tabs)/Home/HomeScreen")} chromeless>
                Skip
            </Button>
        </YStack>
    );
};

async function registerForPushNotificationsAsync(): Promise<string | undefined> {
    let token;

    if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
            name: 'default',
            importance: Notifications.AndroidImportance.MAX,
            vibrationPattern: [0, 250, 250, 250],
            lightColor: '#FF231F7C',
        });
    }

    if (Device.isDevice) {
        const { status: existingStatus } = await Notifications.getPermissionsAsync();
        let finalStatus = existingStatus;
        if (existingStatus !== 'granted') {
            const { status } = await Notifications.requestPermissionsAsync();
            finalStatus = status;
        }
        if (finalStatus !== 'granted') {
            return undefined;
        }
        try {
            const projectId = Constants.expoConfig?.extra?.eas?.projectId ?? Constants.easConfig?.projectId;
            if (!projectId) {
                throw new Error('Project ID not found');
            }
            token = (await Notifications.getExpoPushTokenAsync({ projectId })).data;
        } catch (e) {
            console.error('Error getting push token:', e);
            return undefined;
        }
    } else {
        Alert.alert('Error', 'Must use physical device for Push Notifications');
    }

    return token;
}

export default NotificationPermissionScreen;