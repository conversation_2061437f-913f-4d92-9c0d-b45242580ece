import React, { useState, useEffect } from 'react';
import {
    YStack,
    Label,
    Input,
    Button,
    Spacer,
    Text,
    Avatar,
    H2,
    XStack,
} from "tamagui";
import { SafeAreaView } from "react-native-safe-area-context";
import * as ImagePicker from 'expo-image-picker';
import { Alert, View, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { FontAwesome5 } from '@expo/vector-icons';
import {router, useFocusEffect} from "expo-router";
import {useMutation} from "@tanstack/react-query";
import {completeUserProfile, uploadImage} from "@/api/usersAPI"
import {AxiosError} from "axios";
import AsyncStorage from "@react-native-async-storage/async-storage";
import {requestTrackingPermissionsAsync} from "expo-tracking-transparency";
import MusicProviderSelector from "@/components/MusicProviderSelector";

const USER_FIRST_NAME = "user-first-name";
const USER_LAST_NAME = "user-last-name";

export default function CompleteUserProfileScreen() {
    // const [firstName, setFirstName] = useState<string>('');
    // const [lastName, setLastName] = useState<string>('');
    const [username, setUsername] = useState<string>('');
    const [profileImage, setProfileImage] = useState<string | null>(null);
    const [musicProvider, setMusicProvider] = useState<string>('APPLE_MUSIC');
    const [isLoading, setIsLoading] = useState<boolean>(false);

    useFocusEffect(
        React.useCallback(() => {
            const fetchPermissions = async () => {
                const { status } = await requestTrackingPermissionsAsync();
                if (status === "granted") {
                    console.log("Tracking Permission Granted");
                    await AsyncStorage.setItem("tracking-permission", "granted");
                } else {
                    console.log("Tracking Permission Denied");
                    await AsyncStorage.setItem("tracking-permission", "denied");
                }
            };
            fetchPermissions();
        }, [])
    );

    useEffect(() => {
        (async () => {
            const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
            if (status !== 'granted') {
                Alert.alert('Permission denied', 'We need permission to access your photo library.');
            }
        })();
    }, []);

    const pickImage = async () => {
        let result = await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ImagePicker.MediaTypeOptions.Images,
            allowsEditing: true,
            aspect: [4, 3],
            quality: 1,
        });

        if (!result.canceled) {
            const uri = result.assets[0].uri
            setProfileImage(uri);
            uploadImageMutation.mutate({uri})
        }
    };



    const CompleteUserProfileMutation = useMutation({
        mutationFn: completeUserProfile,
        onSuccess: () => {
            router.replace("/(app)/(onboarding)/OnboardingCarouselScreen");
        },
        onError: (err: AxiosError) => {
            if (err.response?.status == 409){
                Alert.alert("Validation Error", "Username already exists");
            } else {
                Alert.alert("Error", "An error occurred. Please try again later.");
            }
        }
    })

    const uploadImageMutation = useMutation({
        mutationFn: uploadImage,
        onSuccess: () => {
            // console.log("Image Upload Successful")
        },
        onError: (err: AxiosError) => {
            console.log(err)
            Alert.alert("Error", "An error occurred while uploading the image. Please try again later.");
        }
    })


    const handleSubmit = async () => {
        const firstName = await AsyncStorage.getItem(USER_FIRST_NAME)
        const lastName = await AsyncStorage.getItem(USER_LAST_NAME)

        const userData = {
            firstName,
            lastName,
            username,
            musicProvider,
        };

        if (!username) {
            Alert.alert("Validation Error", "Username is required");
            return;
        }
        CompleteUserProfileMutation.mutate(userData)
    };

    return (
        <SafeAreaView style={{ flex: 1 }}>
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            >
                <ScrollView contentContainerStyle={{ flexGrow: 1, justifyContent: 'center', paddingHorizontal: 16 }} showsVerticalScrollIndicator={false}>
                    <YStack
                        width="100%"
                        alignItems="center"
                        paddingTop={20}
                        space
                    >
                        <H2 paddingBottom={25}>Complete your profile</H2>
                        <YStack alignItems="center" justifyContent="center" marginBottom="$2">
                            <YStack position="relative" width={200} height={175} alignItems="center">
                                <Avatar circular size="$13" style={{ marginBottom: 10 }}>
                                    {profileImage ? (
                                        <Avatar.Image
                                            accessibilityLabel="Profile Picture"
                                            src={profileImage}
                                        />
                                    ) : (
                                        <Avatar.Fallback backgroundColor="$yellow10" />
                                    )}
                                </Avatar>
                                <Button
                                    size="$5"
                                    circular
                                    position="absolute"
                                    bottom={0}
                                    right={0}
                                    backgroundColor="rgba(0,0,0,0.5)"
                                    borderRadius={20}
                                    padding={5}
                                    onPress={pickImage}
                                >
                                    <Ionicons name="camera" size={20} color="white" />
                                </Button>
                            </YStack>
                        </YStack>

                        <YStack width="100%">
                            <Label textAlign="left" paddingBottom={10}>Select your music provider</Label>
                            <MusicProviderSelector musicProvider={musicProvider}
                                                   setMusicProvider={setMusicProvider}/>

                            <Spacer size={10} />
                            {/*<Label textAlign="left">First Name</Label>*/}
                            {/*<Input*/}
                            {/*    id="firstName"*/}
                            {/*    placeholder="First Name"*/}
                            {/*    value={firstName}*/}
                            {/*    onChangeText={setFirstName}*/}
                            {/*    width="100%"*/}
                            {/*/>*/}
                            {/*<Spacer size={10} />*/}

                            {/*<Label textAlign="left">Last Name</Label>*/}
                            {/*<Input*/}
                            {/*    id="lastName"*/}
                            {/*    placeholder="Last Name"*/}
                            {/*    value={lastName}*/}
                            {/*    onChangeText={setLastName}*/}
                            {/*    width="100%"*/}
                            {/*/>*/}

                            <Spacer size={10} />

                            <Label textAlign="left">Username</Label>
                            <Input
                                id="username"
                                placeholder="Username"
                                value={username}
                                onChangeText={(text) => setUsername(text.replace(/\s/g, ''))}
                                width="100%"
                            />

                        </YStack>

                        <Spacer size="$4" />
                        <Button onPress={handleSubmit} disabled={isLoading} width="100%">
                            {isLoading ? 'Checking...' : 'Submit'}
                        </Button>
                    </YStack>
                </ScrollView>
            </KeyboardAvoidingView>
        </SafeAreaView>
    );
}
