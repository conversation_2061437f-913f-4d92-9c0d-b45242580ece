import React, { useState, useEffect, useRef } from 'react';
import { ScrollView, SafeAreaView, Keyboard, Animated } from 'react-native';
import { YStack, Text } from 'tamagui';
import SearchBar from '@/components/MusicSearch/SearchBar';
import BookmarkedSongsList from '@/components/MusicSearch/BookmarkedSongsList';
import SearchResultsList from '@/components/MusicSearch/SearchResultsList';
import DeleteDialog from '@/components/MusicSearch/DeleteDialog';
import { useAppleMusicSearch } from '@/hooks/useSearch';
import { useRouter } from 'expo-router';
import { usePostStore } from '@/stores/postStore';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getUserTrackBookmarks, deleteUserTrackBookmark } from '@/api/bookmarksAPI';
import { useToastController } from '@tamagui/toast';

const MusicSearchScreen: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearchEnabled, setIsSearchEnabled] = useState(false);
  const [selectedBookmark, setSelectedBookmark] = useState<any>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const toast = useToastController();
  const queryClient = useQueryClient();
  const router = useRouter();
  const selectSongToPost = usePostStore((state) => state.selectSongToPost);

  // Store swipe animations for each song by isrc
  const swipeAnimations = useRef<{ [key: string]: Animated.Value }>({}).current;

  const { data: songs, isLoading, error } = useAppleMusicSearch({
    searchTerm: searchQuery,
    isEnabled: isSearchEnabled,
  });

  const {
    data: userBookmarkTrack,
    isLoading: isBookmarksLoading,
    error: bookmarksError,
  } = useQuery({
    queryKey: ['userBookmarkTrack'],
    queryFn: getUserTrackBookmarks,
  });

  const { mutate: removeBookmark } = useMutation({
    mutationFn: (bookmarkTrackId: string) => deleteUserTrackBookmark(bookmarkTrackId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['userBookmarkTrack'] });
      toast.show('Bookmark Removed!', {
        message: 'The song has been removed from your bookmarks',
        theme: 'yellow',
      });
    },
    onError: () => {
      toast.show('Error!', {
        message: 'Failed to remove bookmark. Please try again.',
        theme: 'red',
      });
    },
  });

  // Initialize swipe animation for each bookmarked song
  useEffect(() => {
    if (userBookmarkTrack?.bookmarks?.songs) {
      userBookmarkTrack.bookmarks.songs.forEach((song: any) => {
        if (!swipeAnimations[song.isrc]) {
          swipeAnimations[song.isrc] = new Animated.Value(0);
        }
      });
    }
  }, [userBookmarkTrack]);

  // Enable search after 3+ characters with a debounce
  useEffect(() => {
    const handler = setTimeout(
        () => setIsSearchEnabled(searchQuery.length >= 3),
        500
    );
    return () => clearTimeout(handler);
  }, [searchQuery]);

  const handleCancelSearch = () => {
    setSearchQuery('');
    setIsSearchEnabled(false);
    Keyboard.dismiss();
  };

  const handlePostSong = (songItem: any) => {
    let artworkURLWithSize;
    if (songItem.attributes) {
      artworkURLWithSize = songItem.attributes.artwork.url
          .replace('{w}', '300')
          .replace('{h}', '300');
      selectSongToPost({
        id: songItem.id,
        isrc: songItem.attributes.isrc,
        albumName: songItem.attributes.albumName,
        genres: JSON.stringify(songItem.attributes.genreNames),
        artworkURL: artworkURLWithSize,
        appleMusicURL: songItem.attributes.url,
        trackName: songItem.attributes.name,
        artistName: songItem.attributes.artistName,
        audioPreviewURL: songItem.attributes.previews[0].url,
        explicit: songItem.attributes.contentRating === 'explicit',
        caption: '',
      });
    } else {
      artworkURLWithSize = songItem.artworkURL
          .replace('{w}', '300')
          .replace('{h}', '300');
      selectSongToPost({
        id: songItem.id,
        isrc: songItem.isrc,
        albumName: songItem.albumName,
        genres: songItem.genres,
        artworkURL: artworkURLWithSize,
        appleMusicURL: songItem.appleMusicURL,
        trackName: songItem.trackName,
        artistName: songItem.artistName,
        audioPreviewURL: songItem.audioPreviewURL,
        explicit: songItem.explicit,
        caption: '',
      });
    }
    router.push('/PostSongScreen');
  };

  const handleSwipeAction = (song: any) => {
    setSelectedBookmark(song);
    setShowDeleteDialog(true);
  };

  const handleDeleteConfirm = () => {
    if (selectedBookmark) {
      removeBookmark(selectedBookmark.bookmarkTrackId);
      if (swipeAnimations[selectedBookmark.isrc]) {
        Animated.spring(swipeAnimations[selectedBookmark.isrc], {
          toValue: 0,
          useNativeDriver: true,
        }).start();
      }
    }
    setShowDeleteDialog(false);
    setSelectedBookmark(null);
  };

  const handleDeleteCancel = () => {
    if (selectedBookmark && swipeAnimations[selectedBookmark.isrc]) {
      Animated.spring(swipeAnimations[selectedBookmark.isrc], {
        toValue: 0,
        useNativeDriver: true,
      }).start();
    }
    setShowDeleteDialog(false);
    setSelectedBookmark(null);
  };

  return (
      <SafeAreaView style={{ flex: 1 }}>
        <YStack style={{ flex: 1 }}>
          <SearchBar
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              onCancel={handleCancelSearch}
          />
          {searchQuery.length === 0 ? (
              <ScrollView style={{ flex: 1 }} scrollEnabled={!selectedBookmark}>
                <BookmarkedSongsList
                    bookmarks={userBookmarkTrack?.bookmarks}
                    swipeAnimations={swipeAnimations}
                    onSwipeAction={handleSwipeAction}
                    onPostSong={handlePostSong}
                    isLoading={isBookmarksLoading}
                    error={bookmarksError}
                />
              </ScrollView>
          ) : (
              <SearchResultsList
                  songs={songs}
                  isLoading={isLoading}
                  error={error}
                  onPostSong={handlePostSong}
              />
          )}
          <DeleteDialog
              visible={showDeleteDialog}
              onConfirm={handleDeleteConfirm}
              onCancel={handleDeleteCancel}
          />
        </YStack>
      </SafeAreaView>
  );
};

export default MusicSearchScreen;
