import {Tabs} from "expo-router";
import {Platform} from "react-native";
import {AntDesign, FontAwesome, Ionicons, MaterialIcons} from "@expo/vector-icons";
import {BlurView} from "expo-blur";
import {View, Stack, styled, useTheme} from "@tamagui/core";

// Fixed Tamagui styled components
const TabBarContainer = styled(Stack, {
    position: "absolute",
    bottom: Platform.OS === "ios" ? "$2" : "$1",
    left: "$2",
    right: "$2",
    height: 70,
    backgroundColor: Platform.OS === "ios" ? "transparent" : "rgba(20, 20, 20, 0.75)",
    borderRadius: 35,
    shadowColor: "$shadowColor",
    shadowOffset: {width: 0, height: 6},
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 8,
    borderWidth: Platform.OS === "ios" ? 0.5 : 0,
    borderColor: "rgba(255, 255, 255, 0.15)",
    flexDirection: "row",
    paddingHorizontal: "$3", // Increased horizontal padding
    overflow: "hidden",
});

const TabItemContainer = styled(Stack, {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    height: 70,
    alignSelf: "center",
    paddingTop: "$4",
});

const ActiveTabButton = styled(Stack, {
    backgroundColor: "$yellow10",
    borderRadius: 27.5,
    width: 55,
    height: 55,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "rgba(254, 217, 0, 0.6)",
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.8,
    shadowRadius: 8,
    elevation: 6,
    scale: 1,

    variants: {
        pressed: {
            true: {
                scale: 0.95,
            },
        },
    } as const,
});

const InactiveIconContainer = styled(Stack, {
    width: 55,
    height: 55,
    justifyContent: "center",
    alignItems: "center",
    opacity: 0.8,
    alignSelf: "center",
});

export default function TabLayout() {
    return (
        <Tabs
            screenOptions={{
                tabBarShowLabel: false,
                tabBarStyle: {
                    position: "absolute",
                    bottom: Platform.OS === "ios" ? 8 : 4,
                    left: "2%",
                    right: "2%",
                    height: 70,
                    backgroundColor: "transparent",
                    borderRadius: 35,
                    elevation: 0,
                    borderTopWidth: 0,
                    display: 'flex',
                },
                tabBarItemStyle: {
                    height: 70,
                    alignSelf: "center",
                    justifyContent: "center",
                    alignItems: "center",
                },
                tabBarIconStyle: {
                    flex: 1,
                    alignItems: "center",
                    justifyContent: "center",
                },
                headerShown: false,
                tabBarBackground: () => (
                    <TabBarContainer>
                        <BlurView
                            intensity={Platform.OS === 'ios' ? 70 : 100}
                            tint="dark"
                            style={{position: "absolute", top: 0, left: 0, right: 0, bottom: 0}}
                        />
                    </TabBarContainer>
                ),
            }}
        >
            <Tabs.Screen
                name="Home"
                options={{
                    tabBarIcon: ({focused}) => (
                        <TabBarIcon
                            IconComponent={AntDesign}
                            iconName="home"
                            isFocused={focused}
                        />
                    ),
                }}
            />
            <Tabs.Screen
                name="Chat"
                options={{
                    tabBarIcon: ({focused}) => (
                        <TabBarIcon
                            IconComponent={Ionicons}
                            iconName="chatbubble-ellipses-outline"
                            isFocused={focused}
                        />
                    ),
                }}
            />
            <Tabs.Screen
                name="Music"
                options={{
                    tabBarIcon: ({focused}) => (
                        <TabBarIcon
                            IconComponent={FontAwesome}
                            iconName="plus-square-o"
                            isFocused={focused}
                        />
                    ),
                }}
            />
            <Tabs.Screen
                name="Event"
                options={{
                    tabBarIcon: ({focused}) => (
                        <TabBarIcon
                            IconComponent={MaterialIcons}
                            iconName="event"
                            isFocused={focused}
                        />
                    ),
                }}
            />

            <Tabs.Screen
                name="Profile"
                options={{
                    tabBarIcon: ({focused}) => (
                        <TabBarIcon
                            IconComponent={Ionicons}
                            iconName="person-outline"
                            isFocused={focused}
                        />
                    ),
                }}
            />
        </Tabs>
    );
}


function TabBarIcon({
                        IconComponent,
                        iconName,
                        isFocused,
                    }: {
    IconComponent: React.ComponentType<any>;
    iconName: string;
    isFocused: boolean;
}) {
    return (
        <TabItemContainer>
            {isFocused ? (
                <ActiveTabButton
                    animation="bouncy"
                    enterStyle={{scale: 0.8, opacity: 0}}
                    exitStyle={{scale: 0.8, opacity: 0}}
                >
                    <Stack
                        scale={1.1}
                        justifyContent="center"
                        alignItems="center"
                        width="100%"
                        height="100%"
                        marginTop="$1"
                    >
                        <IconComponent
                            name={iconName}
                            size={25}
                            color="#000"
                        />
                    </Stack>
                </ActiveTabButton>
            ) : (
                <InactiveIconContainer
                    animation="lazy"
                    enterStyle={{opacity: 0.5}}
                    exitStyle={{opacity: 0.5}}
                >
                    <Stack
                        justifyContent="center"
                        alignItems="center"
                        width="100%"
                        height="100%"
                        marginTop="$1"
                    >
                        <IconComponent
                            name={iconName}
                            size={25}
                            color="rgba(255, 255, 255, 0.9)"
                        />
                    </Stack>
                </InactiveIconContainer>
            )}
        </TabItemContainer>
    );
}
