import {Stack, useNavigation, useFocusEffect} from 'expo-router';
import {useCallback} from 'react';
import {StyleSheet} from 'react-native';
import BackButton from "@/components/BackButton";
import {useHideTabBar} from "@/hooks/useHideTabBar";

export default function SettingsLayout() {
    useHideTabBar();

    return (
        <Stack>
            <Stack.Screen
                name="EditUserProfile"
                options={{
                    headerShown: true,
                    headerTitle: 'Account Settings',
                    headerLeft: () => <BackButton/>,
                }}
            />
            <Stack.Screen
                name="UserProfileSettings"
                options={{
                    headerShown: true,
                    headerTitle: 'Settings',
                    headerLeft: () => <BackButton/>,
                }}
            />
        </Stack>
    );
}