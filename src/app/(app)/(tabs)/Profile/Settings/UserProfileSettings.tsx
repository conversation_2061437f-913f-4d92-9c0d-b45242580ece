import React, {useState} from 'react';
import { Sc<PERSON>View, StyleSheet, Linking } from 'react-native';
import { YStack, <PERSON>r, Button } from 'tamagui';
import { AntDesign, FontAwesome5, Octicons, Ionicons, MaterialIcons, MaterialCommunityIcons } from '@expo/vector-icons';
import Section from '@/components/Settings/Section';
import SettingItem from '@/components/Settings/SettingItem';
import SocialLink from '@/components/Settings/SocialLink';
import {useRouter} from "expo-router";
import {useAuth} from "@/provider/AuthProvider";
import ConfirmSignOutModal from "@/components/ConfirmSignOutModal";
import {useMutation} from "@tanstack/react-query";
import {deleteUserAccount} from "@/api/usersAPI";



const UserProfileSettings: React.FC = () => {
    const router = useRouter();
    const { onLogout } = useAuth();
    const [modalVisible, setModalVisible] = useState(false);

    const handleLogout  = async () => {
        await onLogout!();
    }

    const openWhatsAppChat = () => {
        const phoneNumber = '+************';
        const url = `whatsapp://send?phone=${phoneNumber}`;
        Linking.openURL(url);
    };

    const openEmailApp = () => {
        const emailAddress = '<EMAIL>';
        const url = `mailto:${emailAddress}`;
        Linking.openURL(url);
    };

    const openWebsite = () => {
        const websiteUrl = 'https://www.wullup.com';
        Linking.openURL(websiteUrl);
    };

    const openTermsOfUse = () => {
        const websiteUrl = 'https://www.wullup.com/terms';
        Linking.openURL(websiteUrl);
    };

    const openPrivacyPolicy = () => {
        const websiteUrl = 'https://www.wullup.com/privacy';
        Linking.openURL(websiteUrl);
    };

    const openImprint = () => {
        const websiteUrl = 'https://www.wullup.com/imprint';
        Linking.openURL(websiteUrl);
    };

    const openTikTokProfile = () => {
        const tikTokUsername = 'wull.up';
        const url = `https://www.tiktok.com/@${tikTokUsername}`;
        Linking.openURL(url);
    };

    const openInstagramProfile = () => {
        const instagramUsername = 'wullup';
        const url = `instagram://user?username=${instagramUsername}`;
        Linking.openURL(url).catch(() => {
            Linking.openURL(`https://www.instagram.com/${instagramUsername}`);
        });
    };

    return (
        <YStack width="100%" height="100%" padding={15}>
            <ScrollView  showsVerticalScrollIndicator={false}>
                <Section title="Account">
                    <SettingItem
                        icon={<Octicons name="person" size={24} color="white" />}
                        title="Profile"
                        onPress={() => { router.push("/(app)/(tabs)/Profile/Settings/EditUserProfile"); }}
                    />
                    <Spacer size={5} />
                    <SettingItem
                        icon={<Ionicons name="notifications" size={24} color="white" />}
                        title="Notifications"
                        onPress={() => { // @ts-ignore
                            router.push("/(app)/NotificationsSettingsScreen"); }}
                    />
                    {/*<Spacer size={5} />*/}
                    {/*<SettingItem*/}
                    {/*    icon={<Ionicons name="notifications-outline" size={24} color="white" />}*/}
                    {/*    title="Notifications"*/}
                    {/*    onPress={() => { router.push("/Profile/ManageUserNotifications" );}}*/}
                    {/*/>*/}
                    {/*<Spacer size={5} />*/}
                    {/*<SettingItem*/}
                    {/*    icon={<MaterialIcons name="person-off" size={24} color="white" />}*/}
                    {/*    title="Blocked Users"*/}
                    {/*    onPress={() => {router.push("/Profile/ManageUserPrivacy");}}*/}
                    {/*/>*/}
                </Section>

                <Spacer size={20} />

                <Section title="Feedback & Support">
                    <SettingItem
                        icon={<MaterialIcons name="email" size={24} color="white" />}
                        title="Email"
                        onPress={openEmailApp}
                    />
                    <Spacer size={5} />
                    <SettingItem
                        icon={<FontAwesome5 name="whatsapp" size={24} color="white" />}
                        title="Whatsapp"
                        onPress={openWhatsAppChat}
                    />
                </Section>

                <Spacer size={20} />

                <Section title="Socials">
                    <SocialLink
                        icon={<FontAwesome5 name="globe" size={24} color="white" />}
                        title="Website"
                        onPress={openWebsite}
                    />
                    <Spacer size={5} />
                    <SocialLink
                        icon={<FontAwesome5 name="tiktok" size={24} color="white" />}
                        title="TikTok"
                        onPress={openTikTokProfile}
                    />
                    <Spacer size={5} />
                    <SocialLink
                        icon={<AntDesign name="instagram" size={24} color="white" />}
                        title="Instagram"
                        onPress={openInstagramProfile}
                    />
                </Section>

                <Spacer size={20} />

                <Section title="Legal">
                    <SettingItem
                        icon={<MaterialIcons name="privacy-tip" size={24} color="white" />}
                        title="Privacy Policy"
                        onPress={openPrivacyPolicy}
                    />
                    <Spacer size={5} />
                    <SettingItem
                        icon={<MaterialCommunityIcons name="file-document-outline" size={24} color="white" />}
                        title="Terms of Service"
                        onPress={openTermsOfUse}
                    />
                    <Spacer size={5} />
                    <SettingItem
                        icon={<MaterialCommunityIcons name="office-building-outline" size={24} color="white" />}
                        title="Imprint"
                        onPress={openImprint}
                    />
                </Section>

                <Spacer size={20} />

                <Button onPress={() => setModalVisible(true)} variant="outlined">
                    Sign out
                </Button>
            </ScrollView>
            <ConfirmSignOutModal
                visible={modalVisible}
                onConfirm={handleLogout}
                onCancel={() => setModalVisible(false)}
            />
        </YStack>
    );
};

export default UserProfileSettings;
