import {Stack} from 'expo-router';
import {useHideTabBar} from "@/hooks/useHideTabBar";
import BackButton from "@/components/BackButton";

export default function FriendsLayout() {

    useHideTabBar();

    return (
        <Stack>
            <Stack.Screen
                name="UserFriends"
                options={{
                    headerShown: true,
                    headerTitle: 'Friends',
                    headerLeft: () => <BackButton/>,
                }}
            />

        </Stack>
    );
}