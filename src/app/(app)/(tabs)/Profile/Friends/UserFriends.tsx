import React, {useState} from 'react';
import {Button, H4, Input, ScrollView, XStack, YStack} from 'tamagui';
import FriendRequestComponent from "@/components/FriendRequestComponent";
import FriendSuggestionsComponent from "@/components/FriendSuggestionsComponent";
import FriendsSearchResult from "@/components/FriendsSearchResult";
import {searchUsers} from '@/api/searchAPI';
import {sendConnectionRequest} from "@/api/connectionsAPI";
import {useMutation, useQuery, useQueryClient} from '@tanstack/react-query';
import {useToastController} from "@tamagui/toast";

const UserFriends = () => {
    const [searchTerm, setSearchTerm] = useState('');
    const queryClient = useQueryClient();
    const toast = useToastController()

    const {data: searchResults, isLoading, isError, refetch} = useQuery({
        queryKey: ['searchUsers', { query: searchTerm, limit: 20, offset: 0 }],
        queryFn: searchUsers,
        enabled: searchTerm.length > 0,
    });

    const {mutateAsync} = useMutation({
        mutationFn: sendConnectionRequest,
        onSuccess: () => {
            void queryClient.invalidateQueries({queryKey: ['searchUsers']});
            handleCancelSearch();
        },
    });

    const handleSearchChange = (text) => {
        setSearchTerm(text);
    };

    const handleCancelSearch = () => {
        setSearchTerm('');
    };

    const handleAddFriend = async (friendId) => {
        try {
            await mutateAsync(friendId);
            toast.show('Request sent', {
                message: "",
                theme: 'green',
            });
        } catch (error: any) {
            if (error.response?.status === 406) {
                toast.show('You already have 10 friends.', {
                    message: "Remove a friend to send more requests.",
                    theme: 'red',
                });
            }
        }
    };

    return (
        <ScrollView showsVerticalScrollIndicator={false}>
            <YStack margin={10}>
                <XStack alignItems="center" marginTop={10}>
                    <Input
                        placeholder={"Find Friends"}
                        value={searchTerm}
                        onChangeText={handleSearchChange}
                        flex={1}
                    />
                    {searchTerm.length > 0 && (
                        <Button marginLeft={10} onPress={handleCancelSearch}>
                            Cancel
                        </Button>
                    )}
                </XStack>

                {searchTerm.length > 0 ? (
                    <FriendsSearchResult
                        searchResults={searchResults?.users || []}
                        isLoading={isLoading}
                        isError={isError}
                        onAddFriend={handleAddFriend}
                    />
                ) : (
                    <>
                        <H4 fontWeight="bold" marginBottom={25} marginTop={25}>Friend Requests</H4>
                        <FriendRequestComponent/>
                        <H4 fontWeight="bold" marginBottom={25} marginTop={25}>People you may know</H4>
                        <FriendSuggestionsComponent/>
                    </>
                )}
            </YStack>
        </ScrollView>
    );
};

export default UserFriends;