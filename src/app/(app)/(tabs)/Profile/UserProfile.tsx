// ProfileScreen.tsx

import React, { useEffect, useState } from 'react';
import {
    Avatar,
    Button,
    H4,
    Spacer,
    Text,
    XStack,
    YStack,
} from 'tamagui';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Link } from 'expo-router';
import FontAwesome5 from '@expo/vector-icons/FontAwesome5';
import { ActivityIndicator, TouchableOpacity, FlatList, Alert } from 'react-native';
import { useQuery, useMutation } from '@tanstack/react-query';
import { getUserData, uploadImage } from '@/api/usersAPI';
import { getUserPostHistory } from '@/api/postsAPI';
import PreviousPostCard from '@/components/PreviousPostCard';
import * as ImagePicker from 'expo-image-picker';
import { AxiosError } from 'axios';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { ConnectionsDialog } from '@/components/ConnectionsDialog';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useRouter } from "expo-router";

export default function ProfileScreen() {
    // State variables for user data
    const [bio, setBio] = useState<string>('');
    const [profileImage, setProfileImage] = useState<string | null>(null);
    const [firstName, setFirstName] = useState<string | null>(null);
    const [lastName, setLastName] = useState<string | null>(null);
    const [username, setUsername] = useState<string>('');
    const router = useRouter();

    // Fetch user data
    const userData = useQuery({
        queryKey: ['userData'],
        queryFn: getUserData,
    });

    useEffect(() => {
        if (userData.data) {
            setBio(userData.data.bio);
            setProfileImage(userData.data.profilePicture);
            setFirstName(userData.data.firstName);
            setLastName(userData.data.lastName);
            setUsername(userData.data.username);
        }
    }, [userData.data]);

    // Fetch user post history
    const userPostHistory = useQuery({
        queryKey: ['userPostHistory'],
        queryFn: getUserPostHistory,
    });

    const sortedUserPostHistory = (userPostHistory.data?.postHistory ?? []).sort(
        (a, b) => new Date(b.postedAt).getTime() - new Date(a.postedAt).getTime()
    );

    // Mutation to upload profile image
    const uploadImageMutation = useMutation({
        mutationFn: uploadImage,
        onSuccess: () => {
            // Image uploaded successfully
        },
        onError: (err: AxiosError) => {
            console.log(err);
            Alert.alert(
                'Error',
                'An error occurred while uploading the image. Please try again later.'
            );
        },
    });

    // Function to pick and upload a new profile image
    const pickImage = async () => {
        let result = await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ImagePicker.MediaTypeOptions.Images,
            allowsEditing: true,
            aspect: [4, 3],
            quality: 1,
        });

        if (!result.canceled) {
            const uri = result.assets[0].uri;
            setProfileImage(uri);
            uploadImageMutation.mutate({ uri });
        }
    };

    return (
        <SafeAreaView style={{ flex: 1 }}>
            <YStack width="100%" alignItems="center" justifyContent="center" flex={1}>
                <XStack justifyContent="space-between" width="100%" padding="$3">
                    <Link href={'/(app)/(tabs)/Profile/Friends/UserFriends'} asChild>
                        <Button size="$4" chromeless>
                            <FontAwesome5 name="user-friends" size={28} color="white" />
                        </Button>
                    </Link>

                    <XStack justifyContent="flex-end">
                        {/*<Link href={'/(app)/(tabs)/Profile/CanvasSettings/canvas'} asChild>*/}
                        {/*    <Button size="$4" chromeless>*/}
                        {/*        <MaterialCommunityIcons name="sticker" size={28} color="white" />*/}
                        {/*    </Button>*/}
                        {/*</Link>*/}
                        <Link href={'/(app)/(tabs)/Profile/Settings/UserProfileSettings'} asChild>
                            <Button size="$4" chromeless>
                                <Ionicons name="settings" size={28} color="white" />
                            </Button>
                        </Link>
                    </XStack>
                </XStack>





                {userData.isLoading ? (
                    <YStack flex={1} justifyContent="center" alignItems="center">
                        <ActivityIndicator size="large" color="#fed900" />
                    </YStack>
                ) : userData.data ? (
                    <>
                        <YStack
                            padding={20}
                            backgroundColor="#141414"
                            borderRadius={16}
                            shadowColor="#000"
                            shadowOpacity={0.3}
                            shadowRadius={12}
                            shadowOffset={{ width: 0, height: 6 }}
                            marginHorizontal={20}
                            width="90%"
                            marginBottom={20}
                        >
                            {/* Profile Picture and Name */}
                            <XStack alignItems="center" marginBottom={15} position="relative">
                                <TouchableOpacity onPress={pickImage}>
                                    <YStack position="relative">
                                        <Avatar circular size="$10">
                                            {profileImage ? (
                                                <Avatar.Image
                                                    accessibilityLabel={'Profile Picture'}
                                                    src={profileImage}
                                                />
                                            ) : (
                                                <Avatar.Fallback backgroundColor="#fed900" />
                                            )}
                                        </Avatar>
                                        <YStack
                                            position="absolute"
                                            bottom={0}
                                            right={0}
                                            backgroundColor="#000000aa"
                                            borderRadius={12}
                                            padding={4}
                                        >
                                            <MaterialIcons name="edit" size={20} color="white" />
                                        </YStack>
                                    </YStack>
                                </TouchableOpacity>

                                <YStack marginLeft={15} flex={1}>
                                    <Text fontSize={24} fontWeight="800" color="#fff">
                                        {firstName} {lastName}
                                    </Text>
                                    <Text fontSize={16} color="#aaa" fontStyle="italic">
                                        @{username}
                                    </Text>

                                    {/* This button now only triggers the dialog when clicked */}
                                    <ConnectionsDialog />
                                </YStack>
                            </XStack>

                            {/* Bio Section */}
                            <YStack marginTop={20}>
                                <Text
                                    fontSize={18}
                                    fontStyle="italic"
                                    color="#ddd"
                                    lineHeight={24}
                                    textAlign="left"
                                >
                                    {bio ? `“${bio.trim()}”` : 'No bio added yet.'}
                                </Text>
                            </YStack>

                            {/* Edit Profile Button */}
                            <Link href="/(app)/(tabs)/Profile/Settings/EditUserProfile" asChild>
                                <Button
                                    marginTop={30}
                                    width="100%"
                                    size="$4"
                                    alignSelf="center"
                                >
                                    Edit Profile
                                </Button>
                            </Link>
                        </YStack>

                        <Spacer size="$5" />
                        <H4 width="90%">Your Previous Bangers</H4>
                        <Spacer size="$2" />

                        {/* User's Post History */}
                        <YStack flex={1} width="97%">
                            {userData.isSuccess && sortedUserPostHistory.length === 0 ? (
                                <Text>You haven't posted any Bangers yet!</Text>
                            ) : (
                                <FlatList
                                    data={sortedUserPostHistory}
                                    keyExtractor={(post) => post.postID}
                                    renderItem={({ item }) => (
                                        <React.Fragment>
                                            <PreviousPostCard
                                                postID={item.postID}
                                                artworkURL={item.artworkURL}
                                                artistName={item.artistName}
                                                trackName={item.trackName}
                                                postRating={item.postRating}
                                                audioPreviewURL={item.audioPreviewURL}
                                                trackURL={item.trackURL}
                                                comments={item.comments}
                                            />
                                        </React.Fragment>
                                    )}
                                    showsVerticalScrollIndicator={false}
                                    contentContainerStyle={{ paddingBottom: 10 }}
                                />
                            )}
                        </YStack>
                    </>
                ) : (
                    <Text>Error loading user data</Text>
                )}
            </YStack>
        </SafeAreaView>
    );
}
