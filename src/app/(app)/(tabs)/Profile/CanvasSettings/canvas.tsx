import React from 'react';
import { <PERSON><PERSON>, Button, YStack } from 'tamagui';
import { Dimensions, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';



// Imagine we have a global sticker store:
import { useStickersStore } from '@/stores/useStickers';
import { Sticker } from '@/components/Sticker';

export default function CanvasScreen() {
    //TODO: change file name
    //TODO: change change the icon loctation
    //TODO: add the canvas to the onboarding process

    const router = useRouter();
    // read selected stickers from a global store
    const stickers = useStickersStore((state) => state.stickers);

    const openStickerSearch = () => {
        router.push('/(app)/(tabs)/Profile/CanvasSettings/sticker-search');
    };

    return (
        <>
                <Button
                    onPress={openStickerSearch}
                    backgroundColor="#fed900"
                    color="black"
                    width={65}
                    height={65}
                    borderRadius={50}
                    position="absolute"
                    right={25}
                    bottom={25}
                    justifyContent="center"
                    alignItems="center"
                >
                    <MaterialCommunityIcons name="sticker-plus" size={30} color="black" />
                </Button>
        <Stack f={1}>
            {/* The canvas background area */}
            <YStack f={1} style={styles.canvas}>
                {stickers.map((sticker) => (
                    <Sticker key={sticker.id} uri={sticker.uri} />
                ))}
            </YStack>
        </Stack>


        </>

    );
}

const styles = StyleSheet.create({
    canvas: {
        width: '100%',
        height: Dimensions.get('window').height - 100,
        backgroundColor: '#00000',
    },
});
