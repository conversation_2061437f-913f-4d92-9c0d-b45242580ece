import {Stack} from 'expo-router';
import {useHideTabBar} from "@/hooks/useHideTabBar";

export default function CanvasSettingsLayout() {
    useHideTabBar();

    return (
        <Stack>
            <Stack.Screen
                name="canvas"
                options={{
                    headerShown: true,
                    headerTitle: 'Canvas Settings',
                    headerBackTitleVisible: false,
                }}
            />
            <Stack.Screen
                name="sticker-search"
                options={{
                    headerShown: true,
                    headerTitle: 'Chose your Giphy Sticker',
                    headerBackTitleVisible: false,
                }}
            />
        </Stack>
    );
}