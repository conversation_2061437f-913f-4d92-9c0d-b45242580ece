import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'expo-router';
import { Input, Stack, YStack, ScrollView, Image, Button } from 'tamagui';
import { TouchableOpacity } from 'react-native';
import { useStickersStore } from '@/stores/useStickers';

const GIPHY_API_KEY = '9jUg74RCWIDbMx1BlFg7ZUhuF2d2lH5l';

export default function StickerSearchScreen() {
    //TODO: add giphy attrubution
    const router = useRouter();
    const addSticker = useStickersStore((state) => state.addSticker);

    const [query, setQuery] = useState('');
    const [results, setResults] = useState<any[]>([]);

    const cacheRef = useRef<Record<string, any[]>>({});

    useEffect(() => {
        if (!query || query.length <= 3) {
            setResults([]);
            return;
        }

        // 1. If we've already cached the query, use it immediately.
        if (cacheRef.current[query]) {
            setResults(cacheRef.current[query]);
            return;
        }

        // 2. Create an AbortController to cancel any ongoing request on cleanup
        const controller = new AbortController();
        const { signal } = controller;

        // 3. Debounce: wait 500ms after the user stops typing before fetching
        const timeoutId = setTimeout(async () => {
            try {
                const res = await fetch(
                    `https://api.giphy.com/v1/gifs/search?api_key=${GIPHY_API_KEY}&q=${encodeURIComponent(
                        query
                    )}&limit=21`,
                    { signal }
                );

                if (!res.ok) {
                    throw new Error(`HTTP error! status: ${res.status}`);
                }

                const data = await res.json();
                // 4. Save to cache and update state
                cacheRef.current[query] = data.data;
                setResults(data.data);
            } catch (error: any) {
                if (error.name === 'AbortError') {
                    // The fetch was aborted; ignore
                    console.log('Fetch aborted for query:', query);
                } else {
                    console.error('Giphy error:', error);
                }
            }
        }, 500);

        // Cleanup: clear timeout and abort fetch when query changes
        return () => {
            clearTimeout(timeoutId);
            controller.abort();
        };
    }, [query]);

    const handleSelectGif = (gif: any) => {
        const gifUrl = gif.images?.original?.url || '';
        // Add to global store
        addSticker({ id: gif.id, uri: gifUrl });
        router.back(); // navigate back to the canvas
    };

    return (
        <Stack f={1} p="$5" bg="$black0">
            <YStack mb="$5">
                <Input
                    placeholder="Search Giphy..."
                    value={query}
                    onChangeText={setQuery}
                />
            </YStack>

            <ScrollView
                contentContainerStyle={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    flexGrow: 1,
                }}
            >
                <YStack flexWrap="wrap" flexDirection="row" justifyContent="center">
                    {results.map((item) => {
                        const previewUrl = item.images?.preview_gif?.url;
                        return (
                            <TouchableOpacity
                                key={item.id}
                                onPress={() => handleSelectGif(item)}
                                style={{ margin: 4 }}
                            >
                                <Image
                                    source={{ uri: previewUrl }}
                                    width={113}
                                    height={113}
                                    resizeMode="cover"
                                />
                            </TouchableOpacity>
                        );
                    })}
                </YStack>
            </ScrollView>

        </Stack>
    );
}