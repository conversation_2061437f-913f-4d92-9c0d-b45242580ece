import React, { useState, useEffect } from 'react';
import {
    YStack,
    XStack,
    Text,
    ScrollView,
    Avatar,
    Circle,
    styled,
    Input,
    Card,
} from 'tamagui';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Chat, Message, User } from '@/types/ChatTypes';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';


const ChatContainer = styled(XStack, {
    name: 'ChatContainer',
    padding: '$4',
    backgroundColor: '$background',
    borderBottomWidth: 1,
    borderBottomColor: '$borderColor',
    pressStyle: {
        backgroundColor: '$backgroundPress',
    },
});

const UnreadBadge = styled(Circle, {
    name: 'UnreadBadge',
    backgroundColor: '#fed900',
    size: '$1.5',
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: '$1',
});

// Mock data
const mockUsers: User[] = [
    { id: '1', name: '<PERSON>', avatar: 'https://i.pravatar.cc/150?img=1', isOnline: true },
    { id: '2', name: '<PERSON>', avatar: 'https://i.pravatar.cc/150?img=2', isOnline: false },
    { id: '3', name: 'Mike Johnson', avatar: 'https://i.pravatar.cc/150?img=3', isOnline: true },
];

const mockChats: Chat[] = [
    {
        id: '1',
        user: mockUsers[0],
        lastMessage: { id: '1', text: 'Hey there!', senderId: '1', timestamp: new Date(), isRead: false },
        unreadCount: 2,
        lastActivity: new Date(),
    },
    {
        id: '2',
        user: mockUsers[1],
        lastMessage: { id: '2', text: 'Are you coming to the party?', senderId: '2', timestamp: new Date(Date.now() - 3600000), isRead: true },
        unreadCount: 0,
        lastActivity: new Date(Date.now() - 3600000),
    },
    {
        id: '3',
        user: mockUsers[2],
        lastMessage: { id: '3', text: 'Check out this new song!', senderId: '3', timestamp: new Date(Date.now() - 86400000), isRead: false },
        unreadCount: 1,
        lastActivity: new Date(Date.now() - 86400000),
    },
];

const mockMessages: Message[] = [
    { id: '1', text: 'Hello!', senderId: '2', timestamp: new Date(), isRead: true },
    { id: '2', text: 'Hey there!', senderId: '1', timestamp: new Date(), isRead: true },
];

interface ChatItemProps {
    chat: Chat
    onPress: (chatId: string) => void
}

const ChatItem: React.FC<ChatItemProps> = ({ chat, onPress }) => {
    const formatTime = (date: Date) => {
        const now = new Date()
        const diff = now.getTime() - date.getTime()
        const hours = Math.floor(diff / (1000 * 60 * 60))

        if (hours < 1) return 'now'
        if (hours < 24) return `${hours}h`
        return `${Math.floor(hours / 24)}d`
    }

    return (
        <ChatContainer onPress={() => onPress(chat.id)}>
            <XStack flex={1} alignItems="center" gap="$3">
                <YStack position="relative">
                    <Avatar circular size="$6">
                        <Avatar.Image src={chat.user.avatar} />
                        <Avatar.Fallback backgroundColor="$gray3">
                            <Text color="$color" fontSize="$6">
                                {chat.user.name.charAt(0).toUpperCase()}
                            </Text>
                        </Avatar.Fallback>
                    </Avatar>
                    {chat.user.isOnline && (
                        <Ionicons
                            position="absolute"
                            bottom={-3}
                            right={-3}
                            name="radio-button-on"
                            size={24}
                            color="#fed900"
                        />
                    )}
                </YStack>

                <YStack flex={1} gap="$1">
                    <XStack justifyContent="space-between" alignItems="center">
                        <Text fontSize="$5" fontWeight="600" color="$color">
                            {chat.user.name}
                        </Text>
                        <YStack alignItems="center" gap="$1">
                            <Text fontSize="$3" color="$placeholderColor">
                                {formatTime(chat.lastActivity)}
                            </Text>
                            {chat.unreadCount > 0 && (
                                <UnreadBadge>
                                    <Text
                                        fontSize="$3"
                                        fontWeight="600"
                                        color="$black1"
                                    >
                                        {chat.unreadCount > 99 ? '99+' : chat.unreadCount}
                                    </Text>
                                </UnreadBadge>
                            )}
                        </YStack>
                    </XStack>

                    {chat.lastMessage && (
                        <Text
                            fontSize="$4"
                            color="$placeholderColor"
                            numberOfLines={1}
                            ellipsizeMode="tail"
                        >
                            {chat.lastMessage.text}
                        </Text>
                    )}
                </YStack>


            </XStack>
        </ChatContainer>
    )
}

export default function ChatOverviewScreen() {
    const [currentScreen, setCurrentScreen] = React.useState<'list' | 'chat'>('list');
    const [selectedChat, setSelectedChat] = React.useState<Chat | null>(null);

    const handleChatSelect = (chatId: string) => {
        const chat = mockChats.find(c => c.id === chatId);
        if (chat) {
            setSelectedChat(chat);
            setCurrentScreen('chat');
            // Navigate to the ChatScreen with the selected chat data
            router.push({
                pathname: '/Chat/ChatScreen',
                params: { chatId: chat.id }
            });
        }
    };

    return (
        <SafeAreaView style={{ flex: 1 }}>
            <YStack flex={1} backgroundColor="$background">
                <XStack
                    padding="$4"
                    backgroundColor="$background"
                    borderBottomWidth={1}
                    borderBottomColor="$borderColor"
                    alignItems="center"
                    justifyContent="space-between"
                >
                    <Text fontSize="$10" fontWeight="bold" color="$color">
                        Chats
                    </Text>
                </XStack>

                <ScrollView flex={1}>
                    {mockChats.map((chat) => (
                        <ChatItem
                            key={chat.id}
                            chat={chat}
                            onPress={handleChatSelect}
                        />
                    ))}
                </ScrollView>
            </YStack>
        </SafeAreaView>
    );
}
