import {Stack} from 'expo-router'

export default function AppEntry() {
    return (
        <Stack>
            <Stack.Screen name="LoadingScreen" options={{headerShown: false}}/>
            <Stack.Screen name="(tabs)" options={{headerShown: false}}/>
            <Stack.Screen name="(onboarding)" options={{headerShown: false}}/>
            <Stack.Screen name="(aux)" options={{headerShown: false}}/>
        </Stack>
    )
}