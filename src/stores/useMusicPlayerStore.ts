import { create } from 'zustand';
import { Audio } from 'expo-av';

interface MusicPlayerState {
  playingUrl: string | null;
  isPlaying: boolean;
  soundObject: Audio.Sound | null;
  loadAndPlayTrack: (url: string) => Promise<void>;
  togglePlayPause: () => Promise<void>;
  unloadTrack: () => Promise<void>;
}

export const useMusicPlayerStore = create<MusicPlayerState>((set, get) => ({
  playingUrl: null,
  isPlaying: false,
  soundObject: null,
  loadAndPlayTrack: async (url) => {
    let { soundObject } = get();
    if (soundObject) {
      await soundObject.unloadAsync();
    }

    await Audio.setAudioModeAsync({
      playsInSilentModeIOS: true,
      allowsRecordingIOS: false,
      shouldDuckAndroid: true,
      staysActiveInBackground: false,
      playThroughEarpieceAndroid: false,
    });

    const { sound } = await Audio.Sound.createAsync(
      { uri: url },
      { shouldPlay: true },
    );
    sound.setOnPlaybackStatusUpdate((status) => {
      if ('didJustFinish' in status && status.didJustFinish) {
        set({ isPlaying: false });
      }
    });
    set({ playingUrl: url, isPlaying: true, soundObject: sound });
  },
  togglePlayPause: async () => {
    const { soundObject, isPlaying } = get();
    if (!soundObject) return;
    if (isPlaying) {
      await soundObject.pauseAsync();
    } else {
      await soundObject.playAsync();
    }
    set({ isPlaying: !isPlaying });
  },
  unloadTrack: async () => {
    const { soundObject } = get();
    if (soundObject) {
      await soundObject.unloadAsync();
      set({ playingUrl: null, isPlaying: false, soundObject: null });
    }
  },
}));
