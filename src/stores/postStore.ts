import { create } from "zustand";


interface Preview {
    url: string;
  }
  
  interface Artwork {
    url: string;
    width: number;
    height: number;
  }
  
  interface MusicItemProps {
    song: {
      id: string; 
      artwork: Artwork;
      name: string;
      artistName: string;
      previews: Preview[];
    };
    previewUrl: string;
  }

interface SongToPost {
  id: string;
  isrc: string;
  albumName: string;
  genres: string;
  artworkURL: string;
  appleMusicURL: string;
  trackName: string;
  artistName: string;
  explicit: boolean;
  audioPreviewURL: string;
  caption: string;
}
  

type State = {
  songToPost: SongToPost | null,
  selectSongToPost: (song: SongToPost) => void,
};

export const usePostStore = create<State>((set) => ({
  songToPost: null,
  selectSongToPost: (song: SongToPost) => set({ songToPost: song }),
}));