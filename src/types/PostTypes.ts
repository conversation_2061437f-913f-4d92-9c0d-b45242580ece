import { Song } from './MusicTypes';

export interface Comment {
    id: string;
    postId: string;
    author: string;
    comment: string;
}

export interface CreatePost {
    title: string;
    comments: Comment[];
}

export interface deletePost {
    postId: string;
}


export interface Post  {
    posterProfilePicture: string;
    artistName: string;
    trackName: string;
    postRating: number;
    posterUsername: string;
    artworkURL: string;
    audioPreviewURL: string;
    postID: string;
    spotifyURL?: string | undefined;
    appleMusicURL?: string | undefined;
    postedAt: string;
    userReaction: "Like" | "Dislike" | null;
    comments: any[];
    audioCommentURL?: string | null;
};

export interface  UserFeed {
    posts: Post[];
};