export interface Artwork {
    url: string;
    width: number;
    height: number;
}

export interface Preview {
    url: string;
}


export interface Song {
    id: string;
    attributes: {
        artwork: Artwork;
        name: string;
        artistName: string;
        previews: Preview[];
    };
}

export interface MusicSearchResponse {
    results: {
        songs: {
            data: Song[];
        };
    };
}