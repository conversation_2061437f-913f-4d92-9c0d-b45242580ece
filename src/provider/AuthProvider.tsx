import {authWithApple, authWithGoogle} from "@/api/authAPI";
import { api, storeAuthTokens } from '@/api/apiConfig';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {router} from "expo-router";
import React, { createContext, useContext, useEffect, useState } from 'react';
import * as SecureStore from "expo-secure-store";

const USER_JWT_TOKEN = "user-token";
const USER_FIRST_NAME = "user-first-name";
const USER_LAST_NAME = "user-last-name";
const REFRESH_TOKEN = "refresh-token";
const IS_INITIAL_SIGN_IN = "is-initial-sign-in";

type AuthProps = {
    userJwtToken: string | null;
    isInitialSignIn: boolean;
    onAppleOAuth: (credentials: any) => Promise<void>;
    onGoogleOAuth: (credentials: any) => Promise<void>;
    onDemoAuth: () => Promise<void>;
    onLogout: () => Promise<void>;
    initialized: boolean;
    checkAuthStatus: () => Promise<boolean>;
}

const AuthContext = createContext<Partial<AuthProps>>({})

export function useAuth() {
    return useContext(AuthContext);
}

export const AuthProvider = ({children}: any) => {
    const [userJwtToken, setUserJWTToken] = useState<string | null>(null);
    const [initialized, setInitialized] = useState(false);
    const [isInitialSignIn, setIsInitialSignIn] = useState<boolean>(false);

    useEffect(() => {
        const initializeAuth = async () => {
            try {
                console.log('Initializing authentication...');

                // Load stored authentication state
                const [storedToken, storedIsInitialSignIn] = await Promise.all([
                    AsyncStorage.getItem(USER_JWT_TOKEN),
                    AsyncStorage.getItem(IS_INITIAL_SIGN_IN),
                ]);

                if (storedToken) {
                    console.log('Stored token found, setting auth state');
                    setUserJWTToken(storedToken);
                    api.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`;

                    // Restore isInitialSignIn state, default to false if not found
                    setIsInitialSignIn(storedIsInitialSignIn === 'true');
                } else {
                    console.log('No stored token found');
                    setUserJWTToken(null);
                    setIsInitialSignIn(false);
                }
            } catch (error) {
                console.error('Error initializing auth:', error);
                setUserJWTToken(null);
                setIsInitialSignIn(false);
            } finally {
                setInitialized(true);
                console.log('Authentication initialization complete');
            }
        };

        initializeAuth();
    }, []);



    const handleAppleOAuth = async (credentials: any) => {
        try {
            const response = await authWithApple(credentials);
            console.log("Apple Login Backend response:", response.userJwtToken);

            setUserJWTToken(response.userJwtToken);
            setIsInitialSignIn(response.isInitialSignIn);

            await Promise.all([
                storeAuthTokens(response.userJwtToken, response.userJwtRefreshToken),
                AsyncStorage.setItem(USER_FIRST_NAME, credentials.fullName.givenName),
                AsyncStorage.setItem(USER_LAST_NAME, credentials.fullName.familyName),
                AsyncStorage.setItem(IS_INITIAL_SIGN_IN, response.isInitialSignIn.toString())
            ]);
        } catch (error: any) {
            throw {error: true, message: error};
        }
    }

    const handleGoogleOAuth = async (credentials: any) => {
        try {
            console.log("handling Google Login");
            const response = await authWithGoogle(credentials);
            console.log("Google Login Backend response:", response.userJwtToken);

            setUserJWTToken(response.userJwtToken);
            setIsInitialSignIn(response.isInitialSignIn);

            await Promise.all([
                storeAuthTokens(response.userJwtToken, response.userJwtRefreshToken),
                AsyncStorage.setItem(IS_INITIAL_SIGN_IN, response.isInitialSignIn.toString())
            ]);
        } catch (error: any) {
            throw {error: true, message: error};
        }
    }

    const handleDemoAuth = async () => {
        try {
            console.log("handling Demo Login");
            const DEMO_JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiZGFjNTIzNDAtNjM5Yy00YjE4LWI2NzktODk5YTFmZDViYzcxIiwiZXhwaXJlcyI6MTczNDA3NzkzMi4xNTIxNzU0fQ.ppnsqUI8ITTdGg0n4k4IwR1NGcAlYlll2LpuRTVTdek';
            const DEMO_REFRESH_TOKEN = 'demo-refresh-token'; // Add a demo refresh token if needed

            setUserJWTToken(DEMO_JWT_TOKEN);
            setIsInitialSignIn(false);

            await Promise.all([
                storeAuthTokens(DEMO_JWT_TOKEN, DEMO_REFRESH_TOKEN),
                AsyncStorage.setItem(IS_INITIAL_SIGN_IN, 'false')
            ]);
        } catch (error: any) {
            throw {error: true, message: error.data};
        }
    }

    const handleLogout = async () => {
        console.log("Logout");
        setUserJWTToken(null);
        setIsInitialSignIn(false);

        await Promise.all([
            AsyncStorage.removeItem(USER_JWT_TOKEN),
            AsyncStorage.removeItem(USER_FIRST_NAME),
            AsyncStorage.removeItem(USER_LAST_NAME),
            AsyncStorage.removeItem(IS_INITIAL_SIGN_IN),
            SecureStore.deleteItemAsync(REFRESH_TOKEN)
        ]);

        api.defaults.headers.common['Authorization'] = '';

        router.replace("/login");
    }

    const checkAuthStatus = async (): Promise<boolean> => {
        try {
            const storedToken = await AsyncStorage.getItem(USER_JWT_TOKEN);
            if (storedToken) {
                setUserJWTToken(storedToken);
                api.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`;
                return true;
            } else {
                setUserJWTToken(null);
                setIsInitialSignIn(false);
                return false;
            }
        } catch (error) {
            console.error('Error checking auth status:', error);
            setUserJWTToken(null);
            setIsInitialSignIn(false);
            return false;
        }
    }

    const value = {
        initialized,
        onGoogleOAuth: handleGoogleOAuth,
        onAppleOAuth: handleAppleOAuth,
        onDemoAuth: handleDemoAuth,
        onLogout: handleLogout,
        userJwtToken,
        isInitialSignIn,
        checkAuthStatus
    }

    return (
        <AuthContext.Provider value={value}>
            {children}
        </AuthContext.Provider>
    )
}