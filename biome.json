{"$schema": "https://biomejs.dev/schemas/1.5.1/schema.json", "organizeImports": {"enabled": false}, "linter": {"enabled": true, "rules": {"correctness": {"useExhaustiveDependencies": "off", "noInnerDeclarations": "off", "noUnnecessaryContinue": "off", "noConstructorReturn": "off"}, "suspicious": {"noImplicitAnyLet": "off", "noConfusingVoidType": "off", "noEmptyInterface": "off", "noExplicitAny": "off", "noArrayIndexKey": "off", "noDoubleEquals": "off", "noConsoleLog": "error", "noAssignInExpressions": "off", "noRedeclare": "off"}, "style": {"noParameterAssign": "off", "noNonNullAssertion": "off", "noArguments": "off", "noUnusedTemplateLiteral": "off", "useDefaultParameterLast": "off", "useConst": "off", "useEnumInitializers": "off", "useTemplate": "off", "useSelfClosingElements": "off"}, "security": {"noDangerouslySetInnerHtml": "off", "noDangerouslySetInnerHtmlWithChildren": "off"}, "performance": {"noDelete": "off", "noAccumulatingSpread": "off"}, "complexity": {"noForEach": "off", "noBannedTypes": "off", "useLiteralKeys": "off", "useSimplifiedLogicExpression": "off", "useOptionalChain": "off"}, "a11y": {"noSvgWithoutTitle": "off", "useMediaCaption": "off", "noHeaderScope": "off", "useAltText": "off", "useButtonType": "off"}}}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineWidth": 90, "ignore": ["**/*/generated-new.ts", "**/*/generated-v2.ts"]}, "javascript": {"formatter": {"trailingComma": "es5", "jsxQuoteStyle": "double", "semicolons": "asNeeded", "quoteStyle": "single"}}}