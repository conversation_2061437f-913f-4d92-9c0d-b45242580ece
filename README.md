yarn start
yarn add

rnfes

eas build:configure
npx expo run:android --no-build-cache --device
npx expo run:android



npm install --global eas-cli
eas login
eas init --id 4bdb7c16-0258-4f9a-baed-454b657f2ee7
eas build:configure

eas build --platform all --auto-submit

eas build --platform ios
eas submit -p ios --latest
eas build --platform ios --auto-submit

eas build --platform android
eas submit -p android --latest
eas build --platform android --auto-submit

npx expo run:android --no-build-cache --device
npx expo run:android --no-build-cache

adb logcat

Over the air update:
expo publisch

before publisching app.json:
for ios: adjust the buildNumber/version
for android: versionCode/version

---

Name:WullupAppleMusicKey
Key ID:FS47J7PZ3Z
Services:Media Services (MusicKit, ShazamKit)

--
Spotify Login code: {"authentication": null, "error": null, "errorCode": null, "params": {"code": "AQBesqYRok8Pa1v8-AKJ-tvnQ1m_m9y2Uwp0A0aCgg-NKfbxXfiLb74Kaoy680KCXrciIsCqqeow7Cm6P4we-xxVFHIo7BeuTLOfUUKlt1psaEFcgq-TiMZSWCBP_kP06aFUCTuI5gkk_w9n0PoOftVt6z9MjcGARTDYNWd9QIEpycFPP9imnbgHRSRNx5qT7NSus68IVOROLHF4Now", "state": "A1bqEN3hns"}, "type": "success", "url": "exp://localhost:8081/?code=AQBesqYRok8Pa1v8-AKJ-tvnQ1m_m9y2Uwp0A0aCgg-NKfbxXfiLb74Kaoy680KCXrciIsCqqeow7Cm6P4we-xxVFHIo7BeuTLOfUUKlt1psaEFcgq-TiMZSWCBP_kP06aFUCTuI5gkk_w9n0PoOftVt6z9MjcGARTDYNWd9QIEpycFPP9imnbgHRSRNx5qT7NSus68IVOROLHF4Now&state=A1bqEN3hns"}

Apple Login credential: {"authorizationCode": "c052d48a144c949c1a558049603cf5aaf.0.rrzvr.eZu3v1INP9CKqK8cRnjscQ", "email": null, "fullName": {"familyName": null, "givenName": null, "middleName": null, "namePrefix": null, "nameSuffix": null, "nickname": null}, "identityToken": "eyJraWQiOiJCaDZIN3JIVm1iIiwiYWxnIjoiUlMyNTYifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IiYEjSvMI9TvqhHzSOuZkrmj5aGFdAS5yvt5DN5pyS5*tBbUCgterze9LrOV2HN6qZ5BQcF3bFLxtrWgoPYWDKYRcThfUMukVexvPhXndAvwKIQdVo0fu_ZhbR0a0N0GpN03d3-eFHEykwOM8h9CRg5MbSqPva7bMDE9Cr9SmYmgUIUtuNrq6nMG6nEM4xB5NbQPP9vmg9100330n9uTa_xU705MIbykeS90cH3_QdiTqSXtpCX8VPv6bufo7gA7-xtA9ournduEy3duWYWcs2CCSL*-CyKYaQ8U3zBycsKu_2ZihdXe4VdVVIko2OZWyVIJ-OAvt2idJATAf-aXcA", "realUserStatus": 1, "state": null, "user": "001951.90778cb08f1849c4a97a9c49ff5d4775.1638"}

---

Backend:

### Important Notes

- Do not delete the migrations folder.
- Ensure the migrations folder is in VSC.

### Starting Docker Compose

#### Starting Services

To start all services defined in your Docker Compose file:

```
docker-compose up
```

This command reads the `docker-compose.yml` file and starts all the services defined in it. By default, it runs in the foreground and shows logs from all the containers.

#### Starting in Detached Mode

To start services in detached mode (running in the background):

```
docker-compose up -d
```

#### Stopping Services

To stop all running services:

```
docker-compose down
```

#### Rebuilding Containers

To rebuild containers, especially after changes in Dockerfiles or dependencies:

```
docker-compose up --build
```

However, with the bind mount in place, there's no need to rebuild the Docker image every time we modify the code. Rebuilding is only necessary when a new dependency is added.

### Testing

#### Running Tests [Containers must be running!]

- Basic run:
  ```
  docker-compose exec web python -m pytest
  ```
- With logging:
  ```
  docker-compose exec web python -m pytest -o log_cli=true
  ```
- With coverage:
  ```
  docker-compose exec web python -m pytest --cov="."
  ```
- With coverage (HTML version):
  ```
  docker-compose exec web python -m pytest --cov="." --cov-report html
  ```

Find the report in `project/htmlcov/index.html`

#### Additional Test Commands

- Disable warnings:
  ```
  docker-compose exec web python -m pytest -p no:warnings
  ```
- Run only the last failed tests:
  ```
  docker-compose exec web python -m pytest --lf
  ```
- Stop after the first failure:
  ```
  docker-compose exec web python -m pytest -x
  ```
- Stop after two failures:
  ```
  docker-compose exec web python -m pytest --maxfail=2
  ```
- List the 2 slowest tests:
  ```
  docker-compose exec web python -m pytest --durations=2
  ```

### Code Formatting

#### Flake8

- Run Flake8 to check the code:
  ```
  docker-compose exec web flake8 .
  ```

#### Black

- Black is a code formatter:
- Check format:
  ```
  docker-compose exec web black . --check
  ```
- Show differences:
  ```
  docker-compose exec web black . --diff
  ```
- Apply changes:
  ```
  docker-compose exec web black .
  ```

#### isort

- Sort imports alphabetically:
- Check sorting:
  ```
  docker-compose exec web isort . --check-only
  ```
- Show differences:
  ```
  docker-compose exec web isort . --diff
  ```
- Apply sorting:
  ```
  docker-compose exec web isort .
  ```

#### Combined Check

- Verify that Flake8, Black, and isort all pass:

```
docker-compose exec web flake8 .
docker-compose exec web black . --check
docker-compose exec web isort . --check-only
```

### Database Operations

- Initialize Aerich:
  ```
  docker-compose exec web aerich init -t app.db.TORTOISE_ORM
  ```
- Initialize DB for development:
  ```
  docker-compose exec web aerich init-db
  ```
- Run DB migrations:

```
docker-compose exec web aerich migrate --name name
```

- Upgrade DB:
  ```
  docker-compose exec web aerich upgrade
  ```
- Access database via PSQL:
  ```
  docker-compose exec web-db psql -U postgres
  ```

### SQL Commands

- Reset sequences in PostgreSQL:

```sql
SELECT setval('sequence_name', (SELECT MAX(id) FROM table_name));
```

seq api key Hgtb901Ha6kZkZDjlK7v

TODO:

setup auto build

_------------------_
minio:

# 59AzE#N%wK^jAT

# wullup-api-server

# Par2QjY6SbpKn85atv5B

# O3N5xgwejeXNUK1BVdH2tpTtpwxodnOd1Y6w0JkO

-********\_\_\_********-





// With hooks
import { usePostHog } from 'posthog-react-native'

const MyComponent = () => {
const posthog = usePostHog()

    useEffect(() => {
        posthog.capture("MyComponent loaded", { foo: "bar" })
    }, [])
}



Sentry:
slug=wullup
projectname=wullup-frontend
DSN=https://<EMAIL>/4506885669584896
auth token=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



local.properties
sdk.dir = C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk